<template>
  <div class="search-outer">
    <div ref="searchRef" :style="searchStyle" class="search" :class="{ 'is-float': isFloating }">
      <div class="search-left">
        <slot></slot>
      </div>
      <div class="search-right">
        <el-button v-if="isShowExpand" size="small" type="text" @click="showMore = !showMore">
          {{ showMore ? '收起' : '展开' }}
        </el-button>
        <slot name="right"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SearchBox',
  props: {
    // 是否默认展开
    defaultExpand: {
      type: Boolean,
      default: false
    },
    // 是否浮动模式
    isFloating: {
      type: Boolean,
      default: false
    },
    // 单行高度（可自定义）
    singleHeight: {
      type: Number,
      default: 40
    },
    // 最小高度
    minHeight: {
      type: Number,
      default: 40
    }
  },
  data() {
    return {
      // 是否显示展开，收起按钮
      isShowExpand: false,
      // 展开收起标志
      showMore: false,
      // 当前实际高度
      currentHeight: 0,
      // 完整内容高度
      fullHeight: 0
    };
  },
  computed: {
    searchStyle() {
      if (this.showMore) {
        return {
          height: 'auto',
          minHeight: `${this.minHeight}px`
        };
      } else {
        return {
          height: `${this.singleHeight}px`,
          minHeight: `${this.minHeight}px`
        };
      }
    }
  },
  watch: {
    showMore(val) {
      this.$nextTick(() => {
        this.updateHeight();
        // 向父组件发送高度变化事件
        this.$emit('height-change', {
          isExpanded: val,
          height: this.currentHeight,
          heightDiff: val ? (this.fullHeight - this.singleHeight) : -(this.fullHeight - this.singleHeight)
        });
      });
    }
  },
  methods: {
    updateHeight() {
      if (this.$refs.searchRef) {
        this.currentHeight = this.$refs.searchRef.offsetHeight;
        // 临时展开获取完整高度
        const originalOverflow = this.$refs.searchRef.style.overflow;
        const originalHeight = this.$refs.searchRef.style.height;

        this.$refs.searchRef.style.overflow = 'visible';
        this.$refs.searchRef.style.height = 'auto';
        this.fullHeight = this.$refs.searchRef.scrollHeight;

        // 恢复原始样式
        this.$refs.searchRef.style.overflow = originalOverflow;
        this.$refs.searchRef.style.height = originalHeight;
      }
    },
    checkShowExpand() {
      this.$nextTick(() => {
        if (this.$refs.searchRef) {
          this.updateHeight();
          // 检查是否需要显示展开按钮
          this.isShowExpand = this.fullHeight > this.singleHeight + 5; // 5px 容差
        }
      });
    },
    // 手动触发高度检查（供外部调用）
    recheckHeight() {
      this.checkShowExpand();
    }
  },
  mounted() {
    this.checkShowExpand();

    if (this.defaultExpand) {
      this.showMore = true;
    }

    // 监听窗口大小变化
    window.addEventListener('resize', this.checkShowExpand);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.checkShowExpand);
  }
};
</script>

<style lang="stylus" scoped>
.search-outer
  box-sizing border-box
  position relative
  padding-top 3px
  color #606266
  background-color #ffffff
  border-radius 5px

.search
  display flex
  justify-content space-between
  align-items flex-start
  padding 8px
  background-color #ffffff
  border-radius 6px
  overflow hidden
  transition height ease-in .3s, min-height ease-in .3s
  box-sizing border-box

  &-left
    flex 1
    display flex
    flex-wrap wrap
    align-items center
    gap 8px
    min-height 24px

  &-right
    display flex
    align-items center
    gap 8px
    flex-shrink 0
    margin-left 8px

.is-float
  box-sizing border-box
  position absolute
  left 0
  right 0
  z-index 5
  box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)

// 响应式处理
@media (max-width: 768px)
  .search
    &-left
      gap 4px
    &-right
      margin-left 4px
</style>