import axios from "@/utils/axios";
export const SUB_APP_CODE = "was-plank";
//工作台-工作总览
export const plateTypeWorkbench = {
  //获取任务总览
  getTaskOverviewList(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/list`, data);
  },
  //获取代办任务列表
  getAgencyTasks(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/detail/list`, data);
  },
  //待办任务详情
  taskDetail(data) {
    return axios({
      url: `/${SUB_APP_CODE}/quTask/detail`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //提交
  submit(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/detail/commit`, data);
  },
  //退回
  back(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/detail/sendBack`, data);
  },
  //获取个税扣款列表
  getIndividualTaxList(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/individualIncomeTax/list`, data);
  },
  //个税扣款统计
  IndividualTaxStatistics(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/individualIncomeTax/statistic`, data);
  },
  //编辑个税扣款
  editIndividualTax(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/individualIncomeTax/edit`, data);
  },
  //获取所属工厂列表
  getBelongFactories(data) {
    return axios.post(
      `/${SUB_APP_CODE}/salary/individualIncomeTax/belongFactories`,
      data
    );
  },
  //新增个税扣款
  addIndividualTax(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/individualIncomeTax/add`, data);
  },
  //删除个税扣款
  deleteIndividualTax(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/individualIncomeTax/delete`, data);
  },
  //个税提交
  IndividualSubmit(data) {
    return axios({
      url: `/${SUB_APP_CODE}/salary/individualIncomeTax/commit`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //个税扣款详情
  viewEdit(data) {
    return axios({
      url: `/${SUB_APP_CODE}/salary/individualIncomeTax/viewEdit`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //获取工资表
  getPayroll(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/total`, data);
  },
  //获取工序汇总表表头
  getProcessPayrollHead(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/getGroupSummaryHead`, data);
  },
  //获取工序汇总表数据
  getProcessPayroll(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/listGroupSummary`, data);
  },
  //新增员工工资
  addSalary(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/addSalary`, data);
  },
  //编辑工资表工序
  editSalaryProcedure(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/editProcess`, data);
  },
  //删除工资表
  deleteSalary(params) {
    return axios({
      url: `/${SUB_APP_CODE}/salary/salary/delete`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params,
    });
  },
  //工资表--查看明细
  lookSalary(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/detail`, data);
  },
  //获取分配表
  getAllocation(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/getAllotByFactoryMonth`, data);
  },
  //编辑分配表
  editAllocation(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/allot/edit`, data);
  },
  //分配表导出
  allotExport(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/allot/export`, data);
  },
  //获取上卡现金表
  getCash(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/getBankCashByFactoryMonth`, data);
  },
  //分厂调整分页获取
  getEditSalaryList(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/factoryEditSalary/getEditList`, data);
  },
  //分厂调整统计
  getStatistic(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/factoryEditSalary/statistics`, data);
  },
  //分厂调整提交
  factoryEditSalarySubmit(data) {
    return axios({
      url: `/${SUB_APP_CODE}/salary/factoryEditSalary/submit`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //分厂调整余留管理
  factoryEditSalaryAdjust(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/factoryEditSalary/adjust`, data);
  },
  //分厂调整明细
  factoryEditSalaryDetail(data) {
    return axios({
      url: `/${SUB_APP_CODE}/salary/factoryEditSalary/editDetail`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //分厂调整新增
  addSaveFactoryEdit(data) {
    return axios.post(
      `/${SUB_APP_CODE}/salary/factoryEditSalary/addSaveFactoryEdit`,
      data
    );
  },
  //分厂调整编辑
  updateSaveFactoryEdit(data) {
    return axios.post(
      `/${SUB_APP_CODE}/salary/factoryEditSalary/updateSaveFactoryEdit`,
      data
    );
  },
  //分厂调整删除
  deleteFactoryEdit(data) {
    return axios({
      url: `/${SUB_APP_CODE}/salary/factoryEditSalary/deleteFactoryEdit`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //分厂调整详情
  viewFactoryEdit(data) {
    return axios({
      url: `/${SUB_APP_CODE}/salary/factoryEditSalary/viewFactoryEdit`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //分厂调整记录表
  adjustmentRecord(data) {
    return axios({
      url: `/${SUB_APP_CODE}/salary/factoryEditSalary/editRecord`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //调整前工资表列表
  beforeSalary(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/before/list`, data);
  },
  //编辑上卡现金信息
  editCach(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/bankCash/edit`, data);
  },
  //结束收集生成工资表
  finishCollecting(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/finishCollecting`, data);
  },
  //强制退回
  forceBack(data) {
    return axios({
      url: `/${SUB_APP_CODE}/quTask/fallback`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //获取待办任务任务阶段
  listProcess(data) {
    return axios({
      url: `/${SUB_APP_CODE}/quTask/listProcess`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //查询工资明细的表头
  listAllColumns(params) {
    return axios({
      url: `/${SUB_APP_CODE}/system/scene/listAllColumns`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params,
    });
  },
  //自定义场景配置新增
  sceneConfigurationAdd(data) {
    return axios.post(`/${SUB_APP_CODE}/system/scene/save`, data);
  },
  //自定义场景配置编辑
  sceneConfigurationEdit(data) {
    return axios.post(`/${SUB_APP_CODE}/system/scene/update`, data);
  },
  //自定义场景列表
  sceneConfigurationList(moduleType) {
    return axios({
      url: `/${SUB_APP_CODE}/system/scene/listAllScene`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: {
        moduleType,
      },
    });
  },
  //自定义场景配置删除
  sceneConfigurationDelete(params) {
    return axios({
      url: `/${SUB_APP_CODE}/system/scene/delete`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params,
    });
  },
  //自定义场景添加配置列
  addColumns(data) {
    return axios.post(`/${SUB_APP_CODE}/system/scene/addColumns`, data);
  },
  //特殊工资单状态统计
  specialStatistics(params) {
    return axios({
      url: `/${SUB_APP_CODE}/salary/salary/specialStatistics`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params,
    });
  },
  //是否具有总览任务操作权限
  overviewPermission(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/hasSummaryTaskPermission`, data);
  },
  //是否具有待办任务操作权限
  agencyPermission(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/hasTodoTaskPermission`, data);
  },
  //工资表明细同步更新
  salaryUpdate(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/syncRefresh`, data);
  },
  //查询考勤中的班组信息
  queryGroup(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/queryGroup`, data);
  },
  //批量备注二
  batchRemark(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/batchRemark`, data);
  },
  //工资表班组查询
  salaryAvailaBleGroups(data) {
    return axios({
      url: `/${SUB_APP_CODE}/salary/salary/availableGroups`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //财务标记提交
  financialMark(data) {
    return axios({
      url: `/${SUB_APP_CODE}/quTask/financialMark`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //汇总表
  summarySheet(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/summarySheet`, data);
  },
  //汇总财务表
  financialSheet(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/financialSheet`, data);
  },
  //编辑汇总财务表
  editSummaryFinance(data) {
    return axios.post(
      `/${SUB_APP_CODE}/salary/salary/editSummaryFinanceSheetDetail`,
      data
    );
  },
  //根据工厂和核算月份查询班组列表
  availableGroups(data) {
    return axios({
      url: `/${SUB_APP_CODE}/salary/salary/availableGroups`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //导出
  exportComon(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/summary/export`, data);
  },
  //工资表打印导出
  exportSalaryPrint(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/exportSalaryPrint`, data);
  },
  //分厂调整导出
  factoryEditSalaryExport(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/factoryEditSalary/export`, data);
  },
  //是否展示分配表及上卡现金表标签栏
  showTabs(data) {
    return axios({
      url: `/${SUB_APP_CODE}/quTask/showTabs`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //提交一审、提交二审、结束收集校验
  checkNode(data) {
    return axios({
      url: `/${SUB_APP_CODE}/quTask/check`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //下一节点
  nextNode(data) {
    return axios({
      url: `/${SUB_APP_CODE}/quTask/goNext`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //动态列头列表
  columnHeads(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/columnHeads`, data);
  },
  //汇总表明细
  viewDetail(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/ManualAccounting/viewDetail`, data);
  },
  //汇总表编辑
  editManualAccounting(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/ManualAccounting/edit`, data);
  },
  //工资表打印表头
  getPrintHeader(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/getPrintHeader`, data);
  },
};
//信息台账
export const plateTypeInformation = {
  //员工管理
  employee: {
    //获取员工列表
    employeeList({ pageSize, pageNum, filterData }) {
      return axios.post(`/${SUB_APP_CODE}/salary/staff/list`, {
        pageSize,
        pageNum,
        filterData,
      });
    },
    //新增或修改员工
    addEmployee(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/staff/saveOrUpdate`, data);
    },
    //删除员工
    deleteEmployee(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/staff/delete`, data);
    },
    //查询历史工号详情
    employeeDetails(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/staff/detail`, data);
    },
    //员工列表统计
    employeeStatistics(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/staff/statistic`, data);
    },
    //身份证解码
    decrypt(str) {
      return axios.get("/security/private/full/decrypt", {
        params: { str },
      });
    },
    //银行卡解码
    idCardDecrypt(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/staff/decode`, data);
    },
    //试用员工身份证解码
    idCardDecodeStaff(data) {
      return axios({
        url: `/${SUB_APP_CODE}/salary/staff/idCardDecode`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //历史工号
    historyStaff(data) {
      return axios({
        url: `/${SUB_APP_CODE}/salary/staff/history`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //解冻
    unfreezeStaff(data) {
      return axios({
        url: `/${SUB_APP_CODE}/salary/staff/unfreezeStaff`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //员工详情
    staffDetail(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/staff/staffDetail`, data);
    },
  },
  //余留管理
  remaining: {
    //获取工厂余留列表
    getRemainingList(data) {
      return axios.post(`/${SUB_APP_CODE}/info/factoryRemain/getRemain`, data);
    },
    //导出工厂余留列表
    exportRemainingList(data) {
      return axios.post(`/${SUB_APP_CODE}/info/factoryRemain/export`, data);
    },
    //添加余留
    addRemaining(data) {
      return axios.post(`/${SUB_APP_CODE}/info/factoryRemain/addNewRemain`, data);
    },
    //获取工厂详细余留
    getDetailedRemaining(data) {
      return axios.post(
        `/${SUB_APP_CODE}/info/factoryRemain/getRemainDetailByFactory`,
        data
      );
    },
    //根据月份获取余留
    getRemainByMonth(data) {
      return axios.post(`/${SUB_APP_CODE}/info/factoryRemain/getRemainByMonth`, data);
    },
  },
  //借支台账
  debitAccount: {
    //借支台账列表
    getDebitAccountList({ pageSize, pageNum, filterData }) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/list`, {
        pageSize,
        pageNum,
        filterData,
      });
    },
    // 借支清单列表
    getDebitDetailAccountList({ pageSize, pageNum, filterData }) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/detailList`, {
        pageSize,
        pageNum,
        filterData,
      });
    },
    // 借支清单统计
    debitListStatistics(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/listStatistics`, data);
    },
    //确认提交
    confirmSubmission(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/confirmCommit`, data);
    },
    //借支台账取消
    confirmCancel(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/cancel`, data);
    },
    //分期还款
    amortizationLoan(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/generatePeriods`, data);
    },
    //借支台账详情
    debitAccountDetails(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/borrowingDetail`, data);
    },
    //借支台账列表统计
    debitLedgerListStatistics(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/statistic`, data);
    },
    //批量确认借支台账
    batchConfirmDebitLedger(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/batchProcess`, data);
    },
    //退回
    back(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/sendBack`, data);
    },
    //退回
    rollBackNoAccounting(data) {
      return axios.post(
        `/${SUB_APP_CODE}/salary/borrowing/rollBackNoAccounting`,
        data
      );
    },

    //提交
    submit(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/commit`, data);
    },
    //获取核算月份
    getAccountingMonth(data) {
      return axios({
        url: `/${SUB_APP_CODE}/salary/borrowing/availableMonth`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //修改实付金额
    updateActualAmount(data) {
      return axios({
        url: `/${SUB_APP_CODE}/salary/borrowing/updateActualAmount`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //批量选择总数统计
    batchConfirmCount(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/batchConfirmCount`, data);
    },
    //变动数据
    obsDebitChangeData(data) {
      return axios.post(`/${SUB_APP_CODE}/obsDebit/changeData`, data);
    },
    //处理变动数据
    handleChangeData(data) {
      return axios({
        url: `/${SUB_APP_CODE}/obsDebit/handle`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //无需分期
    noAccounting(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/noAccounting`, data);
    },
  },
  //奖惩台账
  punishment: {
    //获取奖惩数据
    noAccounting(data) {
      return axios.post(`/${SUB_APP_CODE}/RewardPunishmentLedger/noAccounting`, data);
    },

    //获取奖惩数据
    getPunishmentList(data) {
      return axios.post(`/${SUB_APP_CODE}/info/RewardPunishment/getList`, data);
    },
    //新增奖惩
    addPunishment(data) {
      return axios.post(`/${SUB_APP_CODE}/info/RewardPunishment/saveOrUpdate`, data);
    },
    // 更新状态
    uploadStatus(data) {
      return axios.post(`/${SUB_APP_CODE}/info/RewardPunishment/updateStatus`, data);
    },
    //统计
    rewardsStatistics(data) {
      return axios.post(`/${SUB_APP_CODE}/info/RewardPunishment/statistic`, data);
    },
    //删除奖惩
    deletePunishment(data) {
      return axios({
        url: `/${SUB_APP_CODE}/info/RewardPunishment/delete`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //批量确认变动数据
    multipleHandle(data) {
      return axios.post(`/${SUB_APP_CODE}/hr/rewardPunish/multipleHandle `, data);
    },
  },
  //特殊工资单管理
  payrollManagement: {
    //特殊工资单列表
    specialSalaryList(data) {
      return axios.post(`/${SUB_APP_CODE}/specialSalary/list`, data);
    },
    //特殊工资单统计
    statistics(data) {
      return axios.post(`/${SUB_APP_CODE}/specialSalary/statistics`, data);
    },
    //新增特殊工资单
    addSpecialSalary(data) {
      return axios.post(`/${SUB_APP_CODE}/specialSalary/add`, data);
    },
    //编辑特殊工资单
    editSpecialSalary(data) {
      return axios.post(`/${SUB_APP_CODE}/specialSalary/update`, data);
    },
    //扣款明细
    listDeductDetail(data) {
      return axios.post(`/${SUB_APP_CODE}/specialSalary/listDeductDetail`, data);
    },

    //编辑特殊工资单详情
    editSalaryDetail() {
      return axios({
        url: `/${SUB_APP_CODE}/specialSalary/editSalaryDetail`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      });
    },
    //特殊工资单详情
    salaryDetail(data) {
      return axios({
        url: `/${SUB_APP_CODE}/specialSalary/detail`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //删除特殊工资单详情
    deleteSalaryDetail(data) {
      return axios.post(`/${SUB_APP_CODE}/specialSalary/deleteSalaryDetail`, data);
    },
    //根据月份查询工资单详情
    monthSalary(data) {
      return axios.post(`/${SUB_APP_CODE}/specialSalary/monthSalary`, data);
    },
    //工资单详情修改记录
    salaryDetailLog() {
      return axios({
        url: `/${SUB_APP_CODE}/specialSalary/salaryDetailLog`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      });
    },
    //批量提交
    batchSubmit(data) {
      return axios.post(`/${SUB_APP_CODE}/specialSalary/batchSubmit`, data);
    },
    //提交
    submit(data) {
      return axios({
        url: `/${SUB_APP_CODE}/specialSalary/submit`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //批量审核
    batchAudit(data) {
      return axios.post(`/${SUB_APP_CODE}/specialSalary/batchAudit`, data);
    },
    //退回
    sendBack(data) {
      return axios({
        url: `/${SUB_APP_CODE}/specialSalary/sendBack`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //退回原因
    sendBackReason(data) {
      return axios({
        url: `/${SUB_APP_CODE}/specialSalary/sendBackReason`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //作废
    revoke(data) {
      return axios({
        url: `/${SUB_APP_CODE}/specialSalary/revoke`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //通过
    audit(data) {
      return axios({
        url: `/${SUB_APP_CODE}/specialSalary/audit`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //打印次数
    increment(data) {
      return axios.post(`/${SUB_APP_CODE}/specialSalary/increment`, data);
    },
    //特殊工资单明细列表
    specialSalaryDetail(data) {
      return axios.post(`/${SUB_APP_CODE}/specialSalary/detailList`, data);
    },
  },
  //成本赔偿扣款台账
  costCompensation: {
    //无需核算
    noAccounting(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/noAccounting`, data);
    },
    //成本扣款台账列表
    deductList(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/list`, data);
    },
    //统计
    deductStatistics(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/statistic`, data);
    },
    //成本扣款台账新增
    saveDeduct(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/save`, data);
    },
    //成本扣款台账编辑
    updateDeduct(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/update`, data);
    },
    //成本扣款台账核算
    businessAccounting(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/businessAccounting`, data);
    },
    //成本扣款台账回退
    backBpmDeduct(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/backBpmDeduct`, data);
    },
    //成本扣款台账回退
    rollBackNoAccounting(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/rollBackNoAccounting`, data);
    },
    //成本扣款台账删除
    deleteBpmDeduct(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/deleteBpmDeduct`, data);
    },
    //成本扣款台账批量核算
    batchBusinessAccounting(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/batchBusinessAccounting`, data);
    },
    //成本扣款台账查看详情
    detailDeduct(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/viewDetail`, data);
    },
    //成本扣款台账批量回退
    batchBackBpmDeduct(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/batchBackBpmDeduct`, data);
    },
    //成本扣款台账批量删除
    batchDeleteBpmDeduct(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/batchDeleteBpmDeduct`, data);
    },
    //变动数据
    changeData(data) {
      return axios.post(`/${SUB_APP_CODE}//bpm/cost/changeData`, data);
    },
    //处理变动数据
    handleCost(data) {
      return axios({
        url: `/${SUB_APP_CODE}//bpm/cost/handle`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //实际扣款厂牌下拉
    getAllStaffCode(data) {
      return axios({
        url: `/${SUB_APP_CODE}/salary/staff/getAllStaffCode`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    updateActualAmount(data) {
      return axios({
        url: `/${SUB_APP_CODE}/bpmDeduct/updateActualAmount`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  //未付款台账
  unpaidLedger: {
    //未付款台账列表
    unPaidList(data) {
      return axios.post(`/${SUB_APP_CODE}/unPaid/list`, data);
    },
    //编辑未付款台账
    editUnPaidList(data) {
      return axios.post(`/${SUB_APP_CODE}/unPaid/edit`, data);
    },
    //删除未付款台账
    deleteUnPaid(data) {
      return axios({
        url: `/${SUB_APP_CODE}/unPaid/delete`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //申请付款
    unPaidSupplyPay(data) {
      return axios.post(`/${SUB_APP_CODE}/unPaid/supplyPay`, data);
    },
    //取消申请
    unPaidCancelSupply(data) {
      return axios.post(`/${SUB_APP_CODE}/unPaid/cancelSupply`, data);
    },
    //付款确认
    unPaidPayConfirm(data) {
      return axios.post(`/${SUB_APP_CODE}/unPaid/payConfirm`, data);
    },
    //扣款备注
    unPaidPayRemark(data) {
      return axios.post(`/${SUB_APP_CODE}/unPaid/remark`, data);
    },
    //未付款台账详情
    unPaidDetail(data) {
      return axios({
        url: `/${SUB_APP_CODE}/unPaid/detail`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  //工资查询
  salarySearch: {
    //工资查询列表
    salaryQuery(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/salary/salaryQuery`, data);
    },
    //表头
    salaryQueryColumns() {
      return axios.get(`/${SUB_APP_CODE}/salary/salary/salaryQueryColumns`);
    },
    //导出
    exportSalaryQuery(data, isFilterEmpty) {
      return axios.post(`/${SUB_APP_CODE}/salary/salary/exportSalaryQuery?isFilterEmpty=` + isFilterEmpty, data);
    },
  },
  //奖惩台账
  rewardLedger: {
    //奖惩台账列表
    rewardList(data) {
      return axios.post(`/${SUB_APP_CODE}/RewardPunishmentLedger/getList`, data);
    },
    //HR变动数据
    hrChangeData(data) {
      return axios.post(`/${SUB_APP_CODE}/hr/rewardPunish/hrChangeData`, data);
    },
    //统计
    rewardStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/RewardPunishmentLedger/statistic`, data);
    },
    //新增
    addReward(data) {
      return axios.post(`/${SUB_APP_CODE}/RewardPunishmentLedger/save`, data);
    },
    //奖惩台账明细
    viewDetail(data) {
      return axios({
        url: `/${SUB_APP_CODE}/RewardPunishmentLedger/viewDetail`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //实际扣款厂牌下拉
    getAllStaffCode(data) {
      return axios({
        url: `/${SUB_APP_CODE}/salary/staff/getAllStaffCode`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //删除
    deleteReward(data) {
      return axios({
        url: `/${SUB_APP_CODE}/RewardPunishmentLedger/delete`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //处理变动数据
    rewardPunish(data) {
      return axios({
        url: `/${SUB_APP_CODE}/hr/rewardPunish/handle`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //退回||批量退回
    rollback(data) {
      return axios.post(`/${SUB_APP_CODE}/RewardPunishmentLedger/rollback`, data);
    },
    //退回
    rollBackNoAccounting(data) {
      return axios.post(
        `/${SUB_APP_CODE}/RewardPunishmentLedger/rollBackNoAccounting`,
        data
      );
    },

    //核算
    businessAccounting(data) {
      return axios.post(
        `/${SUB_APP_CODE}/RewardPunishmentLedger/businessAccounting`,
        data
      );
    },
    //批量核算
    batchBusinessAccounting(data) {
      return axios.post(
        `/${SUB_APP_CODE}/RewardPunishmentLedger/batchBusinessAccounting`,
        data
      );
    },
    //奖惩-修改实际扣款金额
    rewardUpdateActualAmount(data) {
      return axios({
        url: `/${SUB_APP_CODE}/RewardPunishmentLedger/updateActualAmount`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //奖惩批量删除
    batchDeleteReward(data) {
      return axios.post(`/${SUB_APP_CODE}/RewardPunishmentLedger/batchDelete`, data);
    },
  },
  //新熟手补贴名单
  newEmployeeSubsidies: {
    //新员工补贴名单-列表接口
    newstaffList(data) {
      return axios.post(`/${SUB_APP_CODE}/newStaff/subsidy/list/list`, data);
    },
    //新员工补贴名单-状态数量统计接口
    newstafftotal(data) {
      return axios.post(`/${SUB_APP_CODE}/newStaff/subsidy/list/total`, data);
    },
    //新员工补贴名单-新增接口
    newstaffsave(data) {
      return axios.post(`/${SUB_APP_CODE}/newStaff/subsidy/list/save`, data);
    },
    //新员工补贴名单-编辑接口
    newstaffedit(data) {
      return axios.post(`/${SUB_APP_CODE}/newStaff/subsidy/list/edit`, data);
    },
    //新员工补贴名单-删除接口
    newstaffdelete(data) {
      return axios.post(`/${SUB_APP_CODE}/newStaff/subsidy/list/delete`, data);
    },
  },
};
//数据上传
export const plateTypeDataUpload = {
  //员工考勤
  employeeAttendance: {
    //获取员工考勤列表
    getEmployeeAttendanceList(data) {
      return axios.post(`/${SUB_APP_CODE}/attend/getAttendanceList`, data);
    },
    //新增员工考勤
    addAttendance(data) {
      return axios.post(`/${SUB_APP_CODE}/attend/add`, data);
    },
    // 编辑修改员工考勤
    reviseEmployee(data) {
      return axios.post(`/${SUB_APP_CODE}/attend/update`, data);
    },
    //员工考勤列表统计
    employeeAttendanceStatistics(data) {
      return axios.post(`/${SUB_APP_CODE}/attend/statistic`, data);
    },
    //删除员工考勤
    deleteEmployee(params) {
      return axios({
        url: `/${SUB_APP_CODE}/attend/delete`,
        method: "get",
        params,
      });
    },
    //根据工厂和核算月份查询班组列表
    getGroups(data) {
      return axios({
        url: `/${SUB_APP_CODE}/attend/groups`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //查询员工关联考勤
    queryStaff(data) {
      return axios.post(`/${SUB_APP_CODE}/attend/queryStaff`, data);
    },
    //根据员工考勤查询班组
    listGroupsByAttend(data) {
      return axios.post(`/${SUB_APP_CODE}/attend/listGroupsByAttend`, data);
    },
  },
  //计件工资
  pieceRateWage: {
    //获取计件工资列表
    getPieceRateWageList({ pageNum, pageSize, filterData }) {
      return axios.post(`/${SUB_APP_CODE}/piece/getPieceWageList`, {
        pageNum,
        pageSize,
        filterData,
      });
    },
    //获取下标总记录
    getSubscriptList(data) {
      return axios.post(`/${SUB_APP_CODE}/piece/statistics`, data);
    },
    //获取同步更新任务状态
    syncTaskStatus(data) {
      return axios.post(`/${SUB_APP_CODE}/piece/syncTaskStatus`, data);
    },
    //计件工资（系统）修改
    systemPiece(data) {
      return axios.post(`/${SUB_APP_CODE}/piece/editSystemAmount`, data);
    },
    //计件工资（调整）修改
    adjustmentPiece(data) {
      return axios.post(`/${SUB_APP_CODE}/piece/editAmount`, data);
    },
    //同步更新
    syncUpdate(data) {
      return axios({
        url: `/${SUB_APP_CODE}/piece/syncUpdate`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    // 获取计件工资调账明细
    adjustDetail(data) {
      return axios.post(`/${SUB_APP_CODE}/piece/adjustDetail`, data);
    },
    // 计件调整
    adjustAmount(data) {
      return axios.post(`/${SUB_APP_CODE}/piece/adjustAmount`, data);
    },
    //根据工厂和核算月份查询班组列表
    getGroups(data) {
      return axios({
        url: `/${SUB_APP_CODE}/attend/groups`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  // 集体账户
  collectiveAccount: {
    //获取集体账户列表
    getCollectiveAccountList({ pageNum, pageSize, filterData }) {
      return axios.post(`/${SUB_APP_CODE}/salary/collectiveAccount/getCollective`, {
        pageNum,
        pageSize,
        filterData,
      });
    },
    //获取下标总记录
    getCollectiveAccount(data) {
      return axios.post(`/${SUB_APP_CODE}/piece/getAllDetail`, data);
    },
    //调账
    accountAdjustment(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/collectiveAccount/adjust`, data);
    },
  },
  //其他补贴
  otherSubsidies: {
    //其他补贴分页查询
    getOtherSubsidyList(data) {
      return axios.post(`/${SUB_APP_CODE}/otherSubsidy/list`, data);
    },
    //其他补贴新增
    getOtherSubsidySave(data) {
      return axios.post(`/${SUB_APP_CODE}/otherSubsidy/save`, data);
    },
    // 其他补贴编辑
    getOtherSubsidyUpdate(data) {
      return axios.post(`/${SUB_APP_CODE}/otherSubsidy/update`, data);
    },
    // 其他补贴删除
    getOtherSubsidyDelete(data) {
      return axios.post(`/${SUB_APP_CODE}/otherSubsidy/delete`, data);
    },
    // 其他补贴提交
    getOtherSubsidySubmit({ id, status }) {
      return axios.post(`/${SUB_APP_CODE}/otherSubsidy/submit`, {
        id,
        status,
      });
    },
  },
  //环境补贴
  environmentSubsidy: {
    // 环境补贴新增
    environmentSubsidyadd(data) {
      return axios.post(`/${SUB_APP_CODE}/environmentSubsidy/add`, data);
    },
    // 环境补贴删除
    environmentSubsidydelete(data) {
      return axios.post(`/${SUB_APP_CODE}/environmentSubsidy/delete`, data);
    },
    //环境补贴编辑
    environmentSubsidyedit(data) {
      return axios.post(`/${SUB_APP_CODE}/environmentSubsidy/edit`, data);
    },
    //环境补贴列表
    environmentSubsidylist(data) {
      return axios.post(`/${SUB_APP_CODE}/environmentSubsidy/list`, data);
    },
    //环境补贴统计
    statistics(data) {
      return axios.post(`/${SUB_APP_CODE}/environmentSubsidy/statistics`, data);
    },
    //根据考勤生成环境补贴
    generate(data) {
      return axios.post(`/${SUB_APP_CODE}/environmentSubsidy/generate`, data);
    },
  },
  //新员工补贴
  employeesubsidies: {
    //新员工补贴列表
    newStaffList(data) {
      return axios.post(`/${SUB_APP_CODE}/newStaff/subsidy/list`, data);
    },
    //新员工补贴新增
    newStaffAdd(data) {
      return axios.post(`/${SUB_APP_CODE}/newStaff/subsidy/add`, data);
    },
    //新员工补贴编辑
    newStaffEdit(data) {
      return axios.post(`/${SUB_APP_CODE}/newStaff/subsidy/edit`, data);
    },
    //新员工补贴删除
    newStaffDelete(data) {
      return axios.post(`/${SUB_APP_CODE}/newStaff/subsidy/delete`, data);
    },
    //新员工补贴统计
    newStaffstatistics(data) {
      return axios.post(`/${SUB_APP_CODE}/newStaff/subsidy/statistics`, data);
    },
    //生成新员工补贴
    newStaffgenerate(data) {
      return axios.post(`/${SUB_APP_CODE}/newStaff/subsidy/generate`, data);
    },
  },
  //新员工补贴20%扣款
  newemployee: {
    //新员工补贴20%列表
    newemployeeList(data) {
      return axios.post(`/${SUB_APP_CODE}/newStaff/deduction/list`, data);
    },
    //新员工补贴20%新增
    newemployeeAdd(data) {
      return axios.post(`/${SUB_APP_CODE}/newStaff/deduction/add`, data);
    },
    //新员工补贴20%编辑
    newemployeeEdit(data) {
      return axios.post(`/${SUB_APP_CODE}/newStaff/deduction/edit`, data);
    },
    //新员工补贴20%删除
    newemployeeDelete(data) {
      return axios.post(`/${SUB_APP_CODE}/newStaff/deduction/delete`, data);
    },
    //新员工补贴20%统计
    newemployeestatistics(data) {
      return axios.post(`/${SUB_APP_CODE}/newStaff/deduction/statistics`, data);
    },
    //新员工补贴20%生成环境补贴
    newemployeeGenerate(data) {
      return axios.post(`/${SUB_APP_CODE}/newStaff/deduction/generate`, data);
    },
  },
  //其他扣款
  OtherDeductions: {
    //其他扣款分页查询
    getotherDeductionList(data) {
      return axios.post(`/${SUB_APP_CODE}/otherDeduction/list`, data);
    },
    //其他扣款新增
    getOtherrDeductionSave(data) {
      return axios.post(`/${SUB_APP_CODE}/otherDeduction/save`, data);
    },
    // 其他扣款编辑
    getOtherDeductionUpdate(data) {
      return axios.post(`/${SUB_APP_CODE}/otherDeduction/update`, data);
    },
    // 其他扣款删除
    getOtherDeductionDelete(data) {
      return axios.post(`/${SUB_APP_CODE}/otherDeduction/delete`, data);
    },
    // 其他扣款提交
    getOtherDeductionSubmit(data) {
      return axios.post(`/${SUB_APP_CODE}/otherDeduction/submit`, data);
    },
    // 其他扣款动态列头
    getOtherDeductionHead({ planid }) {
      return axios({
        url: `/${SUB_APP_CODE}/otherDeduction/head`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: planid,
      });
    },
    // 其他扣款动态列数据
    getOtherDeductionHeadData(data) {
      return axios({
        url: `/${SUB_APP_CODE}/otherDeduction/data`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //其他扣款导入
    Otherimport(data) {
      return axios.post(`/${SUB_APP_CODE}/otherDeduction/import`, data);
    },
  },
  // 社保扣款
  SocialSecurity: {
    // 批量开启或关闭20元社保
    batchOnOrOffInsurance(data) {
      return axios.post(
        `/${SUB_APP_CODE}/salary/socialSecurity/batchOnOrOffInsurance`,
        data
      );
    },
    // 查询社保扣款列表
    getSocialSecurityList(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/socialSecurity/list`, data);
    },
    //批量删除
    batchDelete(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/socialSecurity/batchDelete`, data);
    },
    // 新增-修改社保扣款
    getSocialSecuritySaveOrUpdate(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/socialSecurity/add`, data);
    },
    // 修改社保扣款
    getSocialSecuritySaveOrEdit(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/socialSecurity/edit`, data);
    },
    // 删除社保扣款
    getSocialSecurityDelete(data) {
      return axios({
        url: `/${SUB_APP_CODE}/salary/socialSecurity/delete`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    // 社保扣款列表页面统计
    getSocialSecurityStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/socialSecurity/statistic`, data);
    },
    //20元社保扣款明细
    twenties(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/socialSecurity/twenties`, data);
    },
    //社保扣款查看明细
    viewEdit(data) {
      return axios({
        url: `/${SUB_APP_CODE}/salary/socialSecurity/viewEdit`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  // 未打卡扣款
  noPunchDeduction: {
    //未打卡扣款列表
    getNoPunchDeduction(data) {
      return axios.post(`/${SUB_APP_CODE}/unClock/list`, data);
    },
    //查询考勤中的班组信息
    getListGroups(data) {
      return axios.post(`/${SUB_APP_CODE}/unClock/listGroupsByCommonQuery`, data);
    },
    //新增未打卡扣款
    addUnClock(data) {
      return axios.post(`/${SUB_APP_CODE}/unClock/addUnClock`, data);
    },
    //编辑未打卡扣款
    editNoPunchDeduction(data) {
      return axios.post(`/${SUB_APP_CODE}/unClock/editUnClock`, data);
    },
    //删除未打卡扣款
    deleteUnClock(data) {
      return axios({
        url: `/${SUB_APP_CODE}/unClock/deleteUnClock`,
        method: "get",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //未打卡扣款统计
    statisticsNoPunchDeduction(data) {
      return axios.post(`/${SUB_APP_CODE}/unClock/statistics`, data);
    },
  },
  // 工会费
  unionFee: {
    //工会费列表
    getUnionFee(data) {
      return axios.post(`/${SUB_APP_CODE}/unionFees/list`, data);
    },
    //编辑工会费
    editUnionFee(data) {
      return axios.post(`/${SUB_APP_CODE}/unionFees/editUnionFees`, data);
    },
    //工会费统计
    statistictUnionFee(data) {
      return axios.post(`/${SUB_APP_CODE}/unionFees/statistics`, data);
    },
    //工会费详情
    detailUnionFee(data) {
      return axios({
        url: `/${SUB_APP_CODE}/unionFees/detail`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //批量开启或关闭工会费扣款
    batchOnOrOffUnionFees(data) {
      return axios.post(`/${SUB_APP_CODE}/unionFees/batchOnOrOffUnionFees`, data);
    },
  },
  //厂服扣款
  factoryUniform: {
    //厂服扣款列表
    getFactoryUniform(data) {
      return axios.post(`/${SUB_APP_CODE}/factoryUniform/list`, data);
    },
    //厂服扣款统计
    factoryUniformStatistics(data) {
      return axios.post(`/${SUB_APP_CODE}/factoryUniform/statistics`, data);
    },
  },
  //公司补贴
  coSubsidy: {
    //公司补贴列表
    getCoSubsidy(data) {
      return axios.post(`/${SUB_APP_CODE}/coAllowance/list`, data);
    },
    //公司补贴统计
    coSubsidyStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/coAllowance/statistics`, data);
    },
  },
  //厂牌扣款
  factoryCard: {
    //厂牌扣款列表
    getFactoryCard(data) {
      return axios.post(`/${SUB_APP_CODE}/factoryCard/list`, data);
    },
    //厂牌扣款统计
    factoryCardStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/factoryCard/statistics`, data);
    },
  },
  //住房补贴
  rentalAllowance: {
    //住房补贴列表
    getRentalAllowance(data) {
      return axios.post(`/${SUB_APP_CODE}/rentalAllowance/list`, data);
    },
    //住房补贴统计
    rentalAllowanceStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/rentalAllowance/statistics`, data);
    },
  },
  //全勤奖
  controller: {
    //全勤奖新增
    fullAttendadd(data) {
      return axios.post(`/${SUB_APP_CODE}/fullAttendanceBonus/add`, data);
    },
    //全勤奖列表
    fullAttendlist(data) {
      return axios.post(`/${SUB_APP_CODE}/fullAttendanceBonus/list`, data);
    },
    //全勤奖编辑
    fullAttendedit(data) {
      return axios.post(`/${SUB_APP_CODE}/fullAttendanceBonus/edit`, data);
    },
    //全勤奖删除
    fullAttendelete(data) {
      return axios.post(`/${SUB_APP_CODE}/fullAttendanceBonus/delete`, data);
    },
    //全勤奖统计
    statistics(data) {
      return axios.post(`/${SUB_APP_CODE}/fullAttendanceBonus/statistics`, data);
    },
    //根据考勤生成全勤奖
    generate(data) {
      return axios.post(`/${SUB_APP_CODE}/fullAttendanceBonus/generate`, data);
    },
  },
  //高温补贴
  highTempSubsidy: {
    //高温补贴列表
    subsidylist(data) {
      return axios.post(`/${SUB_APP_CODE}/highTempSubsidy/list`, data);
    },
    //高温补贴新增
    subsidyadd(data) {
      return axios.post(`/${SUB_APP_CODE}/highTempSubsidy/add`, data);
    },
    //高温补贴编辑
    subsidyedit(data) {
      return axios.post(`/${SUB_APP_CODE}/highTempSubsidy/edit`, data);
    },
    //高温补贴删除
    subsidydelete(data) {
      return axios.post(`/${SUB_APP_CODE}/highTempSubsidy/delete`, data);
    },
    //高温补贴统计
    statistics(data) {
      return axios.post(`/${SUB_APP_CODE}/highTempSubsidy/statistics`, data);
    },
    //根据考勤生成高温补贴
    generate(data) {
      return axios.post(`/${SUB_APP_CODE}/highTempSubsidy/generate`, data);
    },
  },
  //熟手补贴
  ldhandsubsidy: {
    //熟手补贴列表
    ldhandList(data) {
      return axios.post(`/${SUB_APP_CODE}/skilledSubsidy/list`, data);
    },
    //熟手补贴统计
    ldhandstatistics(data) {
      return axios.post(`/${SUB_APP_CODE}/skilledSubsidy/statistics`, data);
    },
    //生成熟手补贴
    ldhandgenerate(data) {
      return axios.post(`/${SUB_APP_CODE}/skilledSubsidy/generate`, data);
    },
    //熟手补贴编辑
    ldhandedit(data) {
      return axios.post(`/${SUB_APP_CODE}/skilledSubsidy/edit`, data);
    },
    //熟手补贴删除
    ldhanddelete(data) {
      return axios.post(`/${SUB_APP_CODE}/skilledSubsidy/delete`, data);
    },
    //熟手补贴新增
    ldhandadd(data) {
      return axios.post(`/${SUB_APP_CODE}/skilledSubsidy/add`, data);
    },
  },
  //生活费
  livingCosts: {
    //生活费列表
    getLivingCosts(data) {
      return axios.post(`/${SUB_APP_CODE}/livingCosts/list`, data);
    },
    //生活费统计
    livingCostsStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/livingCosts/statistics`, data);
    },
  },
  //体检费
  physicalExam: {
    //体检费列表
    getPhysicalExam(data) {
      return axios.post(`/${SUB_APP_CODE}/physicalExam/list`, data);
    },
    //体检费统计
    physicalExamStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/physicalExam/statistics`, data);
    },
    //体检费明细
    physicalDetail(data) {
      return axios.post(`/${SUB_APP_CODE}/physicalExam/viewDetail`, data);
    },
    //体检费编辑
    editSave(data) {
      return axios.post(`/${SUB_APP_CODE}/physicalExam/editSave`, data);
    },
  },
  //成本赔偿
  costCompensation: {
    //成本赔偿列表
    getCostCompensation(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeductList/list`, data);
    },
    //成本赔偿统计
    costCompensationStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeductList/statistic`, data);
    },
  },
  //返工扣款
  reworkCut: {
    //返工扣款列表
    getReworkCut(data) {
      return axios.post(`/${SUB_APP_CODE}/reworkCut/list`, data);
    },
    //返工扣款统计
    reworkCutStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/reworkCut/statistics`, data);
    },
  },
  //杂工考勤
  backManAttendance: {
    //杂工考勤列表
    getBackManAttendanceList(data) {
      return axios.post(`/${SUB_APP_CODE}/backManAttendance/list`, data);
    },
    //杂工考勤统计
    backManAttendanceStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/backManAttendance/statistic`, data);
    },
    //杂工考勤新增
    addBackManAttendance(data) {
      return axios.post(`/${SUB_APP_CODE}/backManAttendance/add`, data);
    },
    //杂工考勤编辑
    editBackManAttendance(data) {
      return axios.post(`/${SUB_APP_CODE}/backManAttendance/edit`, data);
    },
    //杂工考勤详情
    backManAttendanceView(data) {
      return axios({
        url: `/${SUB_APP_CODE}/backManAttendance/view`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //删除
    deleteBackManAttendance(data) {
      return axios({
        url: `/${SUB_APP_CODE}/backManAttendance/delete`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
};
// 数据配置
export const plateTypeDataConfiguration = {
  dataConfigList(data) {
    return axios.post(`/${SUB_APP_CODE}/data/config/list`, data);
  },
  dataConfigSave(data) {
    return axios.post(`/${SUB_APP_CODE}/data/config/save`, data);
  },
  dataConfigUpdate(data) {
    return axios.post(`/${SUB_APP_CODE}/data/config/update`, data);
  },
  dataConfigToggle(data) {
    return axios.get(`/${was - plank}/data/config/toggle?id=${data}`);
  },
};
// 系统配置
export const plateTypeSystemConfig = {
  // 未关联配置项查询
  listUnassignedItem(data) {
    return axios.post(`/${SUB_APP_CODE}/data/config/listUnassignedItem`, data);
  },
  //任务配置分页查询
  taskConfigPage({ pageNum, pageSize, filterData }) {
    return axios.post(`/${SUB_APP_CODE}/system/other/plan/list`, {
      pageNum,
      pageSize,
      filterData,
    });
  },
  //任务配置新增
  taskConfigAdd(data) {
    return axios.post(`/${SUB_APP_CODE}/system/other/plan/save`, data);
  },
  // 启用禁用
  taskConfigtToggle(data) {
    return axios.get(`/${was - plank}/system/other/plan/toggle?id=${data}`);
  },
  //任务配置编辑
  taskConfigEdit(data) {
    return axios.post(`/${SUB_APP_CODE}/system/other/plan/update`, data);
  },
  //任务配置删除
  taskConfigDel(data) {
    return axios({
      url: `/${SUB_APP_CODE}/system/other/plan/delete`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //任务配置模板下载
  taskConfigDownload(data) {
    return axios({
      url: `/${SUB_APP_CODE}/system/other/plan/template/download`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      responseType: "blob",
      params: data,
    });
  },
  //页面配置查询
  pageConfig() {
    return axios.post(`/${SUB_APP_CODE}/system/other/page/list`);
  },
  //页面配置空数据隐藏或显示
  pageConfigEmptyData(data) {
    return axios.post(`/${SUB_APP_CODE}/system/other/page/showOrHide`, data);
  },
  //页面配置拖拽排序
  pageConfigdDrag(data) {
    return axios.post(`/${SUB_APP_CODE}/system/other/page/drag`, data);
  },
  //动态列头
  dynamicheader(params) {
    return axios.get(`/${SUB_APP_CODE}/system/other/head`, {
      params,
    });
  },
  getConfigList(taskType) {
    return axios.get(`/${was - plank}/parameter/config/list?taskType=${taskType}`);
  },
  //参数配置修改
  configUpdate(data) {
    return axios.post(`/${SUB_APP_CODE}/parameter/config/update`, data);
  },
  //高温补贴参数配置查询
  configList(data) {
    return axios.post(`/${SUB_APP_CODE}/parameter/config/list`, data);
  },
  //高温补贴参数配置新增/修改
  configsaveOrUpdate(data) {
    return axios.post(`/${SUB_APP_CODE}/parameter/config/saveOrUpdate`, data);
  },
  //高温补贴参数配置删除/批量删除
  configRemove(data) {
    return axios.post(`/${SUB_APP_CODE}/parameter/config/remove`, data);
  },
  //全勤/环境/高温参数配置查询
  getParamConfigGlobal(taskType) {
    return axios.get(
      `/${was - plank}/parameter/config/getParamConfigGlobal?taskType=${taskType}`
    );
  },
  //全勤/环境/高温参数配置修改
  updateParamConfigGlobal(data) {
    return axios.post(
      `/${SUB_APP_CODE}/parameter/config/updateParamConfigGlobal`,
      data
    );
  },
  //熟手补贴列表
  oldhandList(data) {
    return axios.post(`/${SUB_APP_CODE}/newStaff/config/rule/list`, data);
  },
  //熟手补贴新增
  oldhandAdd(data) {
    return axios.post(`/${SUB_APP_CODE}/newStaff/config/rule/add`, data);
  },
  //熟手补贴编辑
  oldhandedit(data) {
    return axios.post(`/${SUB_APP_CODE}/newStaff/config/rule/edit`, data);
  },
  //熟手补贴批量删除
  oldhandbatchDelete(ids) {
    return axios.get(`/${was - plank}/newStaff/config/rule/batchDelete?ids=${ids}`);
  },
  //熟手补贴删除
  oldhanddelete(id) {
    return axios.get(`/${was - plank}/newStaff/config/rule/delete?id=${id}`);
  },
  //熟手参数配置
  // 参数配置列表修改
  oldupdateConfigGlobal(data) {
    return axios.post(
      `/${SUB_APP_CODE}/newStaff/config/rule/updateConfigGlobal`,
      data
    );
  },
  //参数配置全局配置
  oldhandgetConfigGlobal(data) {
    return axios.post(`/${SUB_APP_CODE}/newStaff/config/rule/getConfigGlobal`, data);
  },
  //新员工补贴20%参数配置查询
  employeeConfigGlobal(taskType) {
    return axios.get(
      `/${was - plank}/newStaff/deduction/config/list?taskType=${taskType}`
    );
  },
  //新员工补贴20%参数配置编辑保存
  editConfigGlobal(data) {
    return axios.post(`/${SUB_APP_CODE}/newStaff/deduction/config/update`, data);
  },
};
//系统管理
export const plateTypeSystemManage = {
  //基础信息
  getBasicPermission: {
    //获取所有带有数据权限的工厂
    getBasicPermissionAll(data) {
      return axios.post(`/${SUB_APP_CODE}/quFactory/getDetail`, data);
    },
    //根据工厂查询班组列表
    getBasicGroupList(data) {
      return axios.post(`/${was - plank}/group/list?factoryId=${data}`);
    },
    //获取所有工厂
    getQuFactory(data) {
      return axios({
        url: `/${SUB_APP_CODE}/quFactory/listByAll`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //可用的工厂列表
    availableFactories() {
      return axios.post(`/${SUB_APP_CODE}/system/basicConfig/availableFactories`);
    },
    //获取所有基础信息
    getAllFactory(data) {
      return axios.post(`/${SUB_APP_CODE}/quFactory/list`, data);
    },
    belongFactories(data) {
      return axios.post(
        `/${SUB_APP_CODE}/salary/socialSecurity/belongFactories`,
        data
      );
    },
    // 编辑班组信息
    editGroup(data) {
      return axios.post(`/${SUB_APP_CODE}/group/edit`, data);
    },
    // 新增班组信息
    addGroup(data) {
      return axios.post(`/${SUB_APP_CODE}/group/add`, data);
    },
    // 根据编码查询所属计件工序
    getlistMesProcessByCode(data) {
      return axios.post(`/${SUB_APP_CODE}/factory/mes/process/listMesProcessByCode`, data);
    },
    // 通过ID删除数据
    getBasicPermissionDeleteById(id) {
      return axios({
        url: `/${SUB_APP_CODE}/system/basicConfig/deleteById`,
        id,
      });
    },
    //工厂启用或禁用
    enable(data) {
      return axios({
        url: `/${SUB_APP_CODE}/quFactory/enable`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //工序启用或禁用
    groupEnable(data) {
      return axios({
        url: `/${SUB_APP_CODE}/group/enable`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //根据工厂下的班组
    availableGroup(data) {
      return axios({
        url: `/${SUB_APP_CODE}/group/list`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //根据工厂和核算月份查询班组列表
    listByFactoryId(data) {
      return axios({
        url: `/${SUB_APP_CODE}/system/basicConfig/availableGroups`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  // 数据权限
  dataPermission: {
    // 获取用户数据权限
    getUserPermission(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/userPermission/getUserList`, data);
    },
    editPermission(data) {
      return axios.post(
        `/${SUB_APP_CODE}/salary/userPermission/editPermission`,
        data
      );
    },
  },
  // 系统日志
  systemLog: {
    //分页获取日志列表
    logList(data) {
      return axios.post(`/${SUB_APP_CODE}/log/list`, data);
    },
  },
  //大工序管理
  largeprocess: {
    //大工序列表
    listBigGroup(data) {
      return axios.post(`/${SUB_APP_CODE}/group/listBigGroup`, data);
    },
    //大工序删除
    deleteBigGroup(data) {
      return axios({
        url: `/${SUB_APP_CODE}/group/deleteBigGroup`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
};

//导出
export const plateTypeExportApI = {
  //获取导入模块列表
  getImportModules() {
    return axios({
      url: `/${SUB_APP_CODE}/quTask/listImportModules`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    });
  },
  //获取导入任务
  getImportTask(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/listImportTask`, data);
  },
  otherHead(data) {
    return axios({
      url: `/${SUB_APP_CODE}/system/other/head`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
};
// 统计
export const plateTypeStatiStics = {
  statisticsSalary(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/statistics`, data);
  },
  statisticsBankCash(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/bankCash/statistics`, data);
  },
  statisticsAllot(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/allot/statistics`, data);
  },
  // 计件工资操作权限
  getPieceWagePermission(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/getPieceWagePermission`, data);
  },
  // 其他扣款统计
  statisticsOtherDeduction(data) {
    return axios.post(`/${SUB_APP_CODE}/otherDeduction/statistics`, data);
  },
  // 其他补贴统计
  statisticsOtherSubsidy(data) {
    return axios.post(`/${SUB_APP_CODE}/otherSubsidy/statistics`, data);
  },
  statisticsOthegetCompleteTaskPermissionrSubsidy(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/getCompleteTaskPermission`, data);
  },
};
//报表管理
export const plateTypeReportManagement = {
  //财务汇总表
  financialSummary(data) {
    return axios({
      url: `/${SUB_APP_CODE}/summaryFinancial/list`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //导出
  exportFinancialSummary(data) {
    return axios({
      url: `/${SUB_APP_CODE}/summaryFinancial/export`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //工资分析表
  salaryAnalysis(data) {
    return axios.post(`/${SUB_APP_CODE}/salaryAnalysis/list`, data);
  },
  //工资分析表导出
  exportSalaryAnalysis(data) {
    return axios.post(`/${SUB_APP_CODE}/salaryAnalysis/export`, data);
  },
  //人工效率分析表环比查询
  efficiencylist(data) {
    return axios.post(`/${SUB_APP_CODE}/labor/efficiency/report/list`, data);
  },
  //人工效率分析表同比查询
  yoyEfficiencylist(data) {
    return axios.post(`/${SUB_APP_CODE}/labor/efficiency/report/listOnYear`, data);
  },
  //人工效率分析表导出
  exportEfficiencylist(data) {
    return axios.post(`/${SUB_APP_CODE}/labor/efficiency/report/export`, data);
  },
  //工资划拨分析表明细表
  wageTransferAnalyzeDetailed(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/transfer/report/listDetail`, data);
  },
  //工资划拨分析表汇总表
  wageTransferAnalyzeSummary(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/transfer/report/listSummary`, data);
  },
  //工资划拨分析表导出
  exportWageTransferAnalyze(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/transfer/report/export`, data);
  },
};

//工资管理
export const plateTypePieceWageSystem = {
  //分页获取MES计件工资列表
  getMesPieceWagetList(data) {
    return axios.post(`/${SUB_APP_CODE}/mes/piece/listPieceWageSystem`, data);
  },
  //获取MES计件工资明细
  getMesPieceWagetDetails(data) {
    return axios.post(`/${SUB_APP_CODE}/mes/piece/getPieceWageDetail`, data);
  },
  //MES计件工资列表导出
  exportMesPieceWagetList(data) {
    return axios.post(
      `/${SUB_APP_CODE}/mes/piece/plankPieceWageSystemExport`,
      data
    );
  },
  //获取MES集体计件汇总列表
  getMesPieceworkWageSummary(data) {
    return axios.post(`/${SUB_APP_CODE}/mes/piece/listPieceWageSystemTotal`, data);
  },
  //MES个人明细导出
  exportMesPieceWagetPerson(data) {
    return axios.post(
      `/${SUB_APP_CODE}/mes/piece/plankPieceWagePerson`,
      data
    );
  },
  //MES集体明细导出
  exportMesPieceWagetGroup(data) {
    return axios.post(
      `/${SUB_APP_CODE}/mes/piece/plankPieceWageGroup`,
      data
    );
  },
  // 分页获取个人计件明细列表
  getMesPersonalDetailsList(data) {
    return axios.post(`/${SUB_APP_CODE}/mes/piece/listPieceWagePerson`, data);
  },
  // 分页获取集体计件明细列表
  getMesGroupDetailList(data) {
    return axios.post(`/${SUB_APP_CODE}/mes/piece/listPieceWageGroup`, data);
  },
  //MES计件工资统计
  getMesPieceWagetStatistic(data) {
    return axios.post(`/${SUB_APP_CODE}/mes/piece/statistic`, data);
  },
  //MES计件工资个人明细统计
  getMesPieceWagetPersonalStatistic(data) {
    return axios.post(`/${SUB_APP_CODE}/mes/piece/person/statistic`, data);
  },
  //MES计件工资集体明细统计
  getMesPieceWagetGroupStatistic(data) {
    return axios.post(`/${SUB_APP_CODE}/mes/piece/group/statistic`, data);
  },
  //MES计件工资刷新
  updateMesPieceWage(data) {
    return axios.post(`/${SUB_APP_CODE}/mes/piece/refresh`, data);
  },
  //daily/piece 日均计件统计表
  dailyPieces: {
    // 日均计件统计列表
    getDailyPieceList(data) {
      return axios.post(`/${SUB_APP_CODE}/daily/piece/report/list`, data);
    },
    //获取表头
    getDailyPieceHeader(data) {
      return axios.post(`/${SUB_APP_CODE}/daily/piece/report/getHeader`, data);
    },
    //导出
    exportDailyPieceList(data) {
      return axios.post(`/${SUB_APP_CODE}/daily/piece/report/export`, data);
    },
  },
  //周待办任务
  weekTodoTasks: {
    //周待办任务列表
    getWeekTodoTasksList(data) {
      return axios.post(`/${SUB_APP_CODE}/quTask/week/list`, data);
    },
    //周待办任务结束收集校验
    checkWeekTodoTasks(taskId) {
      return axios.get(`/${SUB_APP_CODE}/quTask/week/checkFinishCollecting?taskId=` + taskId);
    },
    //周待办任务结束收集生成工资表
    nextWeekTodoTasks(taskId) {
      return axios.get(`/${SUB_APP_CODE}/quTask/week/finishCollecting?taskId=` + taskId);
    },
    //周工资表
    getWeekListWeekSalary(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/salary/week/listWeekSalary`, data);
    },
    //周待办任务明细列表
    getWeekTodoTasksDetailList(data) {
      return axios.post(`/${SUB_APP_CODE}/quTask/detail/week/list`, data);
    },
    //周待办任务明细提交
    submitWeekTodoTask(data) {
      return axios.post(`/${SUB_APP_CODE}/quTask/detail/week/commit`, data);
    },
    //周待办任务明细退回
    returnWeekTodoTask(data) {
      return axios.post(`/${SUB_APP_CODE}/quTask/detail/week/sendBack`, data);
    },
    //员工考勤列表
    getAttendanceWeekList(data) {
      return axios.post(`/${SUB_APP_CODE}/attend/week/getAttendanceList`, data);
    },
    //员工考勤删除
    delAttendanceWeek(id) {
      return axios.get(`/${SUB_APP_CODE}/attend/week/delete?id=` + id);
    },
    //员工考勤新增
    addAttendanceWeek(data) {
      return axios.post(`/${SUB_APP_CODE}/attend/week/add`, data);
    },
    //员工考勤编辑
    updateAttendanceWeek(data) {
      return axios.post(`/${SUB_APP_CODE}/attend/week/update`, data);
    },
    //员工考勤统计
    getAttendanceWeekCount(data) {
      return axios.post(`/${SUB_APP_CODE}/attend/week/statistic`, data);
    },
    //员工考勤导入
    importAttendanceWeekCount(data) {
      return axios.post(`/${SUB_APP_CODE}/attend/week/import`, data);
    },
  }

};