<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form size="mini" :inline="true" :model="searchForm" ref="searchForm" label-position="right">
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input clearable v-model.trim="searchForm.staffName" size="mini" @keyup.enter.native="onSearch">
              <template slot="append">
                <search-batch @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)" ref="childrenStaffNames" titleName="员工姓名" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input v-model.trim="searchForm.staffCode" size="mini" clearable @keyup.enter.native="onSearch">
              <template slot="append">
                <search-batch @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)" ref="childrenStaffCodes" titleName="厂牌编号" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="区域:" prop="areaId">
            <el-select v-model="searchForm.areaId" filterable clearable placeholder="请选择工厂名称" @change="onSearch">
              <el-option v-for="item in tabList" :key="item.value" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="核算月份:" class="range" prop="startYearMonth">
            <!-- <el-date-picker v-model="searchForm.accountingMonth" type="month" placeholder="请选择日期" clearable
              @change="onSearch">
            </el-date-picker> -->
            <el-date-picker :clearable="false" v-model="searchForm.startYearMonth" value-format="yyyy-MM" type="month"
              placeholder="开始月份" clearable>
            </el-date-picker>
            <span class="separator">至</span>
            <el-date-picker :clearable="false" v-model="searchForm.endYearMonth" value-format="yyyy-MM" type="month"
              placeholder="结束月份" clearable>
            </el-date-picker>
          </el-form-item>
          <el-form-item label="异常类型:" prop="invalidSearch">
            <el-select @change="onSearch" clearable v-model.trim="searchForm.invalidSearch" placeholder="请选择">
              <el-option v-for="item in invalidOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-right>
        <div class="headerRight">
          <el-checkbox style="color: #0bb78e;margin-right: 10px;" v-model="hiddenAmount"
            @change="changeHiddenAmount">导出时,隐藏金额为0的列</el-checkbox>

          <el-button size="small" type="primary" @click="salaryVisible = true">
            自定义列
          </el-button>
          <el-button size="small" type="primary" @click="handleExport">
            导出
          </el-button>
        </div>
      </template>
      <vxe-table :key="tableKey" ref="tableRef" resizable stripe border :loading="loading" :loading-config="{
        icon: 'vxe-icon-indicator roll',
        text: '正在拼命加载中...',
      }" highlight-hover-row :height="maxTableHeight" :data="tableData">
        <template v-if="isTableShow">
          <vxe-column v-for="column in columnList" :key="column.fieldName" :field="column.fieldName"
            :title="column.columnName" :width="column.width" :fixed="column.fixed" show-overflow>
            <template slot-scope="{ row }">
              <span v-if="column.fieldType == 'double'">{{
                filterDouble(row[column.fieldName])
              }}</span>
              <span v-else-if="
                column.fieldName == 'remark2' && column.fieldType != 'double'
              ">{{ filterData(row.markStatus, row.remark2) }}</span>
              <span v-else-if="list.includes(column.fieldName)">{{ !row[column.fieldName] ? '-' :
                row[column.fieldName] }}</span>
              <div v-else-if="
                ['dailyDiffRatio', 'dailyPieceWageDiffRatio', 'actualDailyDiffRatio'].includes(
                  column.fieldName
                )
              ">
                <span v-if="
                  row[column.fieldName] == 'NA' ||
                  parseFloat(row[column.fieldName]) > '20'
                " style="color: red">{{ row[column.fieldName] }}</span>
                <span v-else>{{
                  ["0%", "0.00%"].includes(row[column.fieldName]) ||
                    row[column.fieldName] == null
                    ? "-"
                    : row[column.fieldName]
                }}</span>
              </div>
              <span v-else>{{ row[column.fieldName] }}</span>
            </template>
            <template slot="header" v-if="
              [
                'dailyWage',
                'processActualDailyWage',
                'personDailyWage',
                'personActualDailyWage',
                'dailyDiffRatio',
                'actualDailyDiffRatio',
                'dailyPieceWage',
                'dailyPieceWageDiffRatio',
                'personDailyPieceWage',
              ].includes(column.fieldName)
            ">
              <span>{{ items[column.fieldName].name }}</span>
              <el-tooltip v-if="items[column.fieldName].name" :content="items[column.fieldName].tips" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </template>
          </vxe-column>
        </template>
      </vxe-table>
      <template v-slot:footer>
        <div style="text-align: right">
          <el-pagination @size-change="onSizeChange" @current-change="onNumChange" :current-page="pageNum"
            :page-size="pageSize" :total="total" :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <custom-config v-if="salaryVisible" @success="hanleSuccess" :visible.sync="salaryVisible"></custom-config>

  </content-panel>
</template>

<script>
import moment from "moment";
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import { calculateTableWidth } from "@/utils";
import customConfig from "./salarySearchCustom/custom-config";
import { moneyFormat } from "@/utils";
import { exceptionEnum } from '@/utils/constant';
//当前场景唯一键名
const SCEN_NAME = '物流工资查询';
export default {
  name: "LogisticsSalarySearch",
  mixins: [tableMixin, pagePathMixin],
  components: {
    customConfig,
  },
  data() {
    return {
      searchForm: {
        invalidSearch: "",
        staffCode: "",
        staffName: "",
        factoryId: "",
        startYearMonth: moment().subtract(1, "months").format("YYYY-MM"),
        endYearMonth: moment().subtract(1, "months").format("YYYY-MM")
      },
      tabList: [],
      columnList: [],
      filterParam: {},
      salaryVisible: false,
      params: {},
      tableData: [],
      isTableShow: true,
      visible: false,//自定义弹窗
      hiddenAmount: true,
      items: Object.freeze({
        dailyWage: {
          name: "工序日均应发",
          tips: "工序日均应发=该厂该月核算班组的总应发工资/该厂该月核算班组的（总出勤天数+杂工加班小时/8）",
        },
        processActualDailyWage: {
          name: "工序日均实发",
          tips: "工序日均实发=该厂该月核算班组的总实发工资/该厂该月核算班组的（总出勤天数+杂工加班小时/8）",
        },
        dailyPieceWage: {
          name: "工序日均计件",
          tips: "工序日均计件=该工序的总【调整后系统计件总工资】/本厂出勤天数",
        },
        personDailyWage: {
          name: "个人日均应发",
          tips: "个人日均应发=该身份证的应发工资/该厂该月核算班组的（总出勤天数+杂工加班小时/8）",
        },
        personActualDailyWage: {
          name: "个人日均实发",
          tips: "个人日均实发=该身份证的实发工资/该厂该月核算班组的（总出勤天数+杂工加班小时/8）",
        },
        personDailyPieceWage: {
          name: "个人日均计件",
          tips: "个人日均计件=该身份证的【调整后系统计件总工资】之和/该身份证的本厂出勤天数",
        },
        dailyDiffRatio: {
          name: "日均应发差异率",
          tips: "（个人日均应发-工序日均应发）/工序日均应发*100%，保留两位小数（四舍五入）",
        },
        actualDailyDiffRatio: {
          name: "日均实发差异率",
          tips: "（个人日均实发-工序日均实发）/工序日均实发*100%，保留两位小数（四舍五入）",
        },
        dailyPieceWageDiffRatio: {
          name: "日均计件差异率",
          tips: "（个人日均计件-工序日均计件）/工序日均计件*100%，保留两位小数（四舍五入）",
        },
      }),
      list: Object.freeze(['totalWorkDay', 'totalWorkHour', 'workdayWorkDay', 'workdayWorkHour', 'nightOvertimeHour', 'weekendWork', 'holidayWork', 'totalWorkHourWithOvertime', 'workHour']),
      loading: false,
      resizeOffset: 55,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      tableKey: ""
    };
  },
  computed: {       
    invalidOptions() {
      return exceptionEnum.filter((item) => {
        return item.value !== "划出工资大于3000";
      });
    },
  },
  async created() {
    this.$api.logisticsSystemManage.getBasicPermission
      .getBasicPermissionAll().then((res) => {
        this.tabList = res.data.map((item) => ({
          label: item.name,
          name: item.name,
          id: item.id,
        })) || [];
      });
    this.onSearch();
  },
  // watch: {
  //   $route: {
  //     handler(value) {
  //       if (value.path.includes("salarySearch") && value.path.includes("logistics")) {
  //         this.getList();
  //       }
  //     },
  //     deep: true,
  //   },
  // },
  methods: {
    changeHiddenAmount(val) {
      this.hiddenAmount = val;
    },
    //获取表头
    getColumns() {
      return this.$api.logisticsInformation.salarySearch.salaryQueryColumns().then(({ data }) => {
        const columns = data || [];
        const hasFactoryName = columns.some(col => col.fieldName === "beStringFactoryName");
        const hasAccountingMonth = columns.some(col => col.fieldName === "accountingMonth");
        const abcIndex = columns.findIndex(item => item.fieldName === "serialNumber");

        if (abcIndex !== -1 && !hasFactoryName && !hasAccountingMonth) {
          columns.splice(abcIndex + 1, 0,
            {
              columnName: "工厂名称",
              fieldName: "factoryName",
              fixed: "left",
            },
            {
              columnName: "核算月份",
              fieldName: "accountingMonth",
              fixed: "left",
            }
          );
        } else if (abcIndex === -1 && !hasFactoryName && !hasAccountingMonth) {
          columns.splice(0, 0,
            {
              columnName: "工厂名称",
              fieldName: "factoryName",
              fixed: "left",
            },
            {
              columnName: "核算月份",
              fieldName: "accountingMonth",
              fixed: "left",
            }
          );
        }
        this.columnList = columns;
        this.handleColumns();
      });
    },
    //获取表头
    async getScenColumns() {
      const { data } = await this.$api.logisticsWorkbench.sceneConfigurationList("SalaryQuery");
      try {
        this.columnList = [];
        const list = data.filter((item) => item.name === SCEN_NAME);

        if (!list.length || list[0].columns.length == 0) {
          await this.getColumns();
          return;
        }

        const columns = [...list[0].columns];
        const hasFactoryName = columns.some(col => col.fieldName === "beStringFactoryName");
        const hasAccountingMonth = columns.some(col => col.fieldName === "accountingMonth");


        const abcIndex = columns.findIndex(item => item.fieldName === "serialNumber");
        if (abcIndex !== -1 && !hasFactoryName && !hasAccountingMonth) {
          columns.splice(abcIndex + 1, 0,
            {
              columnName: "工厂名称",
              fieldName: "factoryName",
              fixed: "left",
            },
            {
              columnName: "核算月份",
              fieldName: "accountingMonth",
              fixed: "left",
            }
          );
        } else if (abcIndex === -1 && !hasFactoryName && !hasAccountingMonth) {
          columns.splice(0, 0,
            {
              columnName: "工厂名称",
              fieldName: "factoryName",
              fixed: "left",
            },
            {
              columnName: "核算月份",
              fieldName: "accountingMonth",
              fixed: "left",
            }
          );
        }

        this.columnList = columns;
        await this.$nextTick();
        await this.handleColumns();

      } catch (error) {
        console.error('获取列配置失败:', error);
        this.columnList = [];
      } finally {
        this.loading = false;
      }
    },
    async hanleSuccess() {
      await this.getScenColumns(); // 重新获取列配置
      this.tableKey = Math.random();// 强制刷新表格
    },
    //获取列表
    getList() {
      this.loading = true;
      this.$api.logisticsInformation.salarySearch
        .salaryQuery({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            ...this.filterParam,
            ...this.params,
          },
        })
        .then(({ data: { list, total } }) => {
          this.isTableShow = false;
          this.$nextTick(() => {
            this.tableData = list.map((item, index) => ({ ...item, serialNumber: index + 1 })) || [];
            this.getScenColumns();
            this.total = total;
            this.tableKey = Math.random();
          });
        });
    },
    handleColumns() {
      let items = {
        serialNumber: "60",
        staffName: "100",
        staffCode: "120",
        idCard: "140"
      };
      this.columnList = this.columnList.map((item) => {
        if (Object.keys(items).includes(item.fieldName)) {
          Object.keys(items).forEach((key) => {
            if (key == item.fieldName) {
              item.width = items[item.fieldName];
            }
          });
        } else {
          item.width = this.flexWidth(
            item.fieldName,
            this.tableData,
            item.columnName
          );
        }
        if (["序号", "员工姓名", "厂牌编号"].includes(item.columnName)) {
          item.fixed = "left";
        }
        return item;
      });
      let totalWidth = this.columnList.reduce((pre, cur) => {
        return (pre += Number(cur.width));
      }, 0);
      if (totalWidth <= this.$refs.tableRef.$el.clientWidth) {
        this.columnList.forEach((item) => {
          delete item.width;
        });
      }
      this.isTableShow = true;
      this.loading = false;
    },
    filterData(value, remark) {
      if (!["其他（暂不发放）", "其他", '正常'].includes(value)) return value;
      if (remark) {
        if (value == "其他（暂不发放）") {
          return `${value}-${remark}`;
        } else {
          return remark;
        }
      } else {
        if (value == "其他（暂不发放）") {
          return `${value}`;
        } else {
          return "";
        }
      }
    },
    handleCancel(type) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
    },
    filterDouble(value) {
      if (!value) return '-';
      return moneyFormat(value);
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        // if (key == "accountingMonth") {
        //   if (moment.isDate(val)) {
        //     this.filterParam.accountingMonth = moment(val).format("YYYY-MM");
        //   }
        // } else if (typeof val !== "undefined" && val !== null && val !== "") {
        //   this.filterParam[key] = val;
        // }
        if (key == 'startYearMonth' && val) {
          this.filterParam.startYearMonth = moment(val).format('YYYY-MM') || '';
        } else if (key == 'endYearMonth' && val) {
          this.filterParam.endYearMonth = moment(val).format('YYYY-MM') || '';
        } else if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    //计算表格列宽度
    flexWidth(...args) {
      return calculateTableWidth(...args);
    },
    //导出
    handleExport() {
      let params = {
        ...this.filterParam,
        ...this.params,
      };
      let isFilterEmpty = this.hiddenAmount ? 1 : 0;
      this.$api.logisticsInformation.salarySearch.exportSalaryQuery(params, isFilterEmpty).then(() => {
        this.$message.success('导出操作成功，请前往导出记录查看详情');
      });
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>

<style lang="stylus" scoped></style>
