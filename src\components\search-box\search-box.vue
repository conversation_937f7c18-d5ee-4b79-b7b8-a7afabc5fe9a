<template>
  <div class="search-outer">
    <div ref="searchRef" :style="searchStyle" class="search" :class="{ 'is-float': isFloating }">
      <div class="search-left">
        <slot></slot>
      </div>
      <div class="search-right">
        <el-button v-if="isShowExpand" size="small" type="text" @click="showMore = !showMore">
          {{ showMore ? '收起' : '展开' }}
        </el-button>
        <slot name="right"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SearchBox',
  props: {
    // 是否默认展开
    defaultExpand: {
      type: Boolean,
      default: false
    },
    // 是否浮动模式
    isFloating: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 是否显示展开，收起按钮
      isShowExpand: false,
      // 展开收起标志
      showMore: false,
      // 展示是增加的高度
      addedHeight: 0,
      // 单行高度（动态计算）
      singleHeight: 40
    };
  },
  computed: {
    searchScrollHeight() {
      return this.$refs.searchRef ? this.$refs.searchRef.scrollHeight : 0;
    },
    searchScrollWidth() {
      return this.$refs.searchRef ? this.$refs.searchRef.scrollWidth : 0;
    },
    searchStyle() {
      return {
        height: this.showMore
          ? 'auto'
          : `${this.singleHeight}px`,
        minHeight: `${this.singleHeight}px`
      };
    }
  },
  watch: {
    showMore(val) {
      if (!this.isFloating) {
        this.$nextTick(() => {
          this.updateTableHeight(val);
        });
      }
    }
  },
  methods: {
    // 计算实际的单行高度
    calculateSingleHeight() {
      if (!this.$refs.searchRef) return;

      // 临时设置为auto获取自然高度
      const originalHeight = this.$refs.searchRef.style.height;
      const originalOverflow = this.$refs.searchRef.style.overflow;

      this.$refs.searchRef.style.height = 'auto';
      this.$refs.searchRef.style.overflow = 'visible';

      // 获取内容的自然高度
      const naturalHeight = this.$refs.searchRef.scrollHeight;

      // 恢复原始样式
      this.$refs.searchRef.style.height = originalHeight;
      this.$refs.searchRef.style.overflow = originalOverflow;

      // 设置合理的单行高度（至少40px）
      this.singleHeight = Math.max(40, naturalHeight);
    },

    updateTableHeight(isShowMore) {
      this.$nextTick(() => {
        if (!this.$refs.searchRef) return;

        const fullHeight = isShowMore ? this.searchScrollHeight : this.singleHeight;

        // 计算高度差
        const heightDiff = isShowMore
          ? (fullHeight - this.singleHeight)
          : -(this.addedHeight);

        // 发送高度变化事件
        if (this.$bus && Math.abs(heightDiff) > 2) { // 2px 容差
          this.$bus.$emit('table.updateHeight', heightDiff);
        }

        // 记录当前增加的高度
        this.addedHeight = isShowMore ? (fullHeight - this.singleHeight) : 0;
      });
    },

    checkShowExpand() {
      this.$nextTick(() => {
        if (!this.$refs.searchRef) return;

        // 重新计算单行高度
        this.calculateSingleHeight();

        // 检查是否需要展开按钮
        const needExpand = this.searchScrollHeight > this.singleHeight + 10; // 10px 容差
        const hasHorizontalScroll = this.searchScrollWidth > (document.documentElement.clientWidth - 250);

        this.isShowExpand = needExpand || hasHorizontalScroll;
      });
    },

    // 手动重新检查高度（供外部调用）
    recheckHeight() {
      this.checkShowExpand();
    }
  },
  mounted() {
    // 延迟执行，确保DOM完全渲染
    this.$nextTick(() => {
      setTimeout(() => {
        this.checkShowExpand();

        if (this.defaultExpand) {
          this.showMore = true;
        }
      }, 50);
    });

    // 监听窗口大小变化
    window.addEventListener('resize', this.checkShowExpand);
  },

  beforeDestroy() {
    window.removeEventListener('resize', this.checkShowExpand);
  }
};
</script>

<style lang="stylus" scoped>
.search-outer
  box-sizing border-box
  position relative
  padding-top 3px
  color #606266
  background-color #ffffff
  border-radius 5px

.search
  display flex
  justify-content space-between
  align-items flex-start
  padding 8px
  background-color #ffffff
  border-radius 6px
  overflow hidden
  transition height ease-in .3s, min-height ease-in .3s
  box-sizing border-box

  &-left
    flex 1
    display flex
    flex-wrap wrap
    align-items center
    gap 8px

  &-right
    display flex
    align-items center
    gap 8px
    flex-shrink 0
    margin-left 8px

.is-float
  box-sizing border-box
  position absolute
  left 0
  right 0
  z-index 5
  box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)

// 响应式处理
@media (max-width: 768px)
  .search
    &-left
      gap 4px
    &-right
      margin-left 4px
</style>