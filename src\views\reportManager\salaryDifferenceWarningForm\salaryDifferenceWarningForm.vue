<template>
  <div>
    <content-panel>
      <template v-slot:search>
        <search-box class="search-box" ref="searchBoxRef">
          <el-form size="mini" :inline="true" :model="searchForm" :rules="rules" ref="searchForm">
            <el-form-item label="工厂名称:" prop="factoryId">
              <el-select v-model="searchForm.factoryId" filterable placeholder="请选择工厂" @change="onSearch">
                <el-option v-for="item in tabList" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="range" label="核算月份:" prop="startYearMonth">
              <el-date-picker v-model="searchForm.startYearMonth" value-format="yyyy-MM" type="month" placeholder="开始月份"
                @change="validateDateRange">
              </el-date-picker>
              <span class="separator">至</span>
              <el-form-item prop="endYearMonth" class="nested-form-item">
                <el-date-picker v-model="searchForm.endYearMonth" value-format="yyyy-MM" type="month" placeholder="结束月份"
                  @change="validateDateRange">
                </el-date-picker>
              </el-form-item>
            </el-form-item>
            <el-form-item label="员工姓名:" prop="staffName">
              <el-input clearable v-model.trim="searchForm.staffName" size="mini" @keyup.enter.native="onSearch">
                <template slot="append">
                  <search-batch @seachFilter="onSearch('staffNames', $event)"
                    @focusEvent="focusEvent('staffNames', $event)" ref="childrenStaffNames" titleName="员工姓名" />
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="厂牌编号:" prop="staffCode">
              <el-input v-model.trim="searchForm.staffCode" size="mini" clearable @keyup.enter.native="onSearch">
                <template slot="append">
                  <search-batch @seachFilter="onSearch('staffCodes', $event)"
                    @focusEvent="focusEvent('staffCodes', $event)" ref="childrenStaffCodes" titleName="厂牌编号" />
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="异常类型:" prop="invalidSearch">
              <el-select @change="onSearch" clearable v-model="searchForm.invalidSearch" placeholder="请选择">
                <el-option v-for="item in invalidOptions" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <template v-slot:right>
            <el-button size="small" type="primary" @click="onSearch">
              查询
            </el-button>
            <el-button size="small" type="warning" @click="resetSearchForm">
              重置
            </el-button>
          </template>
        </search-box>
      </template>
      <table-panel ref="tablePanel">
        <!-- 隐藏金额为0的列 -->
        <template v-slot:header-right>
          <div class="headerRight">
            <el-checkbox style="color: #0bb78e;margin-right: 10px;" v-model="hiddenAmount"
              @change="changeHiddenAmount">导出时,隐藏金额为0的列</el-checkbox>
            <!-- <el-button size="small" type="primary" @click="visible = true">
              自定义列
            </el-button> -->
            <el-button size="small" type="primary" @click="salaryVisible = true">
              自定义列
            </el-button>
            <el-button size="small" type="primary" @click="handleExport">
              导出
            </el-button>
          </div>
        </template>
        <vxe-table :key="tableKey" ref="tableRef" resizable stripe border :loading="loading" :loading-config="{
          icon: 'vxe-icon-indicator roll',
          text: '正在拼命加载中...',
        }" highlight-hover-row :height="maxTableHeight" :data="tableData">
          <template v-if="isTableShow">
            <vxe-column v-for="column in columnList" :key="column.fieldName" :field="column.fieldName"
              :title="column.columnName" :width="column.width" :fixed="column.fixed" show-overflow>
              <template slot="header" v-if="column.columnName == '日均工资'">
                <span>日均工资</span>
                <el-tooltip content="日均工资=该厂该月核算班组的总应发工资/该厂该月核算班组的总出勤天数" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </template>
              <template slot-scope="{ row }">
                <span v-if="!column.type">
                  <div v-if="column.fieldType == 'double'">
                    <span v-if="
                      column.fieldName == 'leftover' &&
                      filterIncomingSalary(row[column.fieldName])
                    " style="color: red">
                      {{ filterDouble(row[column.fieldName]) }}
                    </span>
                    <span v-else> {{ filterDouble(row[column.fieldName]) }}</span>
                  </div>
                  <span v-else-if="
                    column.fieldName == 'remark2' &&
                    column.fieldType != 'double'
                  ">{{ filterData(row.markStatus, row.remark2) }}</span>
                  <span v-else-if="list.includes(column.fieldName)">{{
                    !row[column.fieldName] ? "-" : row[column.fieldName]
                  }}</span>
                  <div v-else-if="
                    ['dailyDiffRatio', 'dailyPieceWageDiffRatio', 'actualDailyDiffRatio'].includes(
                      column.fieldName
                    )
                  ">
                    <span v-if="
                      row[column.fieldName] == 'NA' ||
                      parseFloat(row[column.fieldName]) > '20'
                    " style="color: red">{{ row[column.fieldName] }}</span>
                    <span v-else>{{
                      ["0%", "0.00%"].includes(row[column.fieldName]) ||
                        row[column.fieldName] == null
                        ? "-"
                        : row[column.fieldName]
                    }}</span>
                  </div>
                  <span v-else>{{ row[column.fieldName] }}</span>
                </span>
              </template>
              <template slot="header" v-if="
                [
                  'dailyWage',
                  'processActualDailyWage',
                  'personDailyWage',
                  'personActualDailyWage',
                  'dailyDiffRatio',
                  'actualDailyDiffRatio',
                  'dailyPieceWageDiffRatio',
                  'dailyPieceWage',
                  'personDailyPieceWage',
                ].includes(column.fieldName)
              ">
                <span>{{ items[column.fieldName].name }}</span>
                <el-tooltip v-if="items[column.fieldName].name" :content="items[column.fieldName].tips" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </template>
            </vxe-column>
          </template>
        </vxe-table>
        <template v-slot:footer>
          <div style="text-align: right">
            <el-pagination @size-change="onSizeChange" @current-change="onNumChange" :current-page="pageNum"
              :page-size="pageSize" :total="total" :page-sizes="[50, 100, 200]"
              layout="total, sizes, prev, pager, next, jumper">
            </el-pagination>
          </div>
        </template>
      </table-panel>
      <!-- <custom-dialog v-if="visible" :visible="visible" @cancel="handleCancel"></custom-dialog> -->
      <custom-config v-if="salaryVisible" @success="hanleSuccess" :visible.sync="salaryVisible"></custom-config>
    </content-panel>
  </div>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import customDialog from "./compontent/customDialog";
import customConfig from "./compontent/custom-config";
import { calculateTableWidth } from "@/utils";
import { moneyFormat, moneyDelete } from "@/utils";
import moment from "moment";
//当前场景唯一键名
const SCEN_NAME = '定制工资差异预警表';
export default {
  name: "CustomizedSalaryDifferenceWarningForm",
  mixins: [tableMixin],
  components: { customDialog, customConfig },
  data() {
    // 自定义验证器：检查开始月份和结束月份是否同时存在
    const validateYearMonth = (rule, value, callback) => {
      if (rule.field === 'startYearMonth') {
        // 如果开始月份有值，结束月份必须有值
        if (value && !this.searchForm.endYearMonth) {
          callback(new Error('请选择结束月份'));
        } else {
          callback();
        }
      } else if (rule.field === 'endYearMonth') {
        // 如果结束月份有值，开始月份必须有值
        if (value && !this.searchForm.startYearMonth) {
          callback(new Error('请选择开始月份'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };

    return {
      searchForm: {
        factoryId: "",
        invalidSearch: "",
        staffCode: "",
        staffName: "",
        startYearMonth: moment().subtract(1, "months").format("YYYY-MM"),
        endYearMonth: moment().subtract(1, "months").format("YYYY-MM")
      },
      // 表单验证规则
      rules: {
        startYearMonth: [
          { validator: validateYearMonth, trigger: 'change' }
        ],
        endYearMonth: [
          { validator: validateYearMonth, trigger: 'change' }
        ]
      },
      hiddenAmount: true,
      salaryVisible: false,
      procedureRules: {
        procedure: [
          { message: "工序不能为空", trigger: "change", required: true },
        ],
      },
      formData: {},
      tableData: [],
      invalidOptions: [
        { label: "日均实发差异率大于20%", value: "日均实发差异率大于20%" },
        { label: "日均计件差异率大于20%", value: "日均计件差异率大于20%" },
      ],
      loading: false,
      filterParam: {},
      //全部表格头
      columnList: [],
      list: Object.freeze([
        "totalWorkDay",
        "totalWorkHour",
        "workdayWorkDay",
        "workdayWorkHour",
        "nightOvertimeHour",
        "weekendWork",
        "holidayWork",
        "totalWorkHourWithOvertime",
        "workHour",
      ]),
      pageSize: 50,
      pageNum: 1,
      total: 0,
      resizeOffset: 55,
      params: {},
      tabList: [],
      isTableShow: true,
      idList: [],
      items: Object.freeze({
        dailyWage: {
          name: "工序日均应发",
          tips: "工序日均应发=该厂该月核算班组的总应发工资/该厂该月核算班组的（总出勤天数+杂工加班小时/8）",
        },
        processActualDailyWage: {
          name: "工序日均实发",
          tips: "工序日均实发=该厂该月核算班组的总实发工资/该厂该月核算班组的（总出勤天数+杂工加班小时/8）",
        },
        dailyPieceWage: {
          name: "工序日均计件",
          tips: "工序日均计件=该工序的总【调整后系统计件总工资】/本厂出勤天数",
        },
        personDailyWage: {
          name: "个人日均应发",
          tips: "个人日均应发=该身份证的应发工资/该厂该月核算班组的（总出勤天数+杂工加班小时/8）",
        },
        personActualDailyWage: {
          name: "个人日均实发",
          tips: "个人日均实发=该身份证的实发工资/该厂该月核算班组的（总出勤天数+杂工加班小时/8）",
        },
        personDailyPieceWage: {
          name: "个人日均计件",
          tips: "个人日均计件=该身份证的【调整后系统计件总工资】之和/该身份证的本厂出勤天数",
        },
        dailyDiffRatio: {
          name: "日均应发差异率",
          tips: "（个人日均应发-工序日均应发）/工序日均应发*100%，保留两位小数（四舍五入）",
        },
        actualDailyDiffRatio: {
          name: "日均实发差异率",
          tips: "（个人日均实发-工序日均实发）/工序日均实发*100%，保留两位小数（四舍五入）",
        },
        dailyPieceWageDiffRatio: {
          name: "日均计件差异率",
          tips: "（个人日均计件-工序日均计件）/工序日均计件*100%，保留两位小数（四舍五入）",
        },
      }),
      tableKey: 0,
    };
  },
  async created() {
    await this.getFactory();
    this.onSearch();
  },

  mounted() {
    // 在组件挂载后，检查表单验证状态
    this.$nextTick(() => {
      // 验证表单，如果有错误则展开搜索框
      this.$refs.searchForm.validate((valid) => {
        if (!valid) {
          this.expandSearchBox();
        }
      });
    });
  },
  methods: {
    //获取表头
    getColumns() {
      return this.$api.information.salarySearch.salaryQueryColumns().then(({ data }) => {
        const columns = data || [];
        const hasFactoryName = columns.some(col => col.fieldName === "beStringFactoryName");
        const hasAccountingMonth = columns.some(col => col.fieldName === "accountingMonth");
        const abcIndex = columns.findIndex(item => item.fieldName === "serialNumber");

        if (abcIndex !== -1 && !hasFactoryName && !hasAccountingMonth) {
          columns.splice(abcIndex + 1, 0,
            {
              columnName: "工厂名称",
              fieldName: "beStringFactoryName",
              fixed: "left",
            },
            {
              columnName: "核算月份",
              fieldName: "accountingMonth",
              fixed: "left",
            }
          );
        } else if (abcIndex === -1 && !hasFactoryName && !hasAccountingMonth) {
          columns.splice(0, 0,
            {
              columnName: "工厂名称",
              fieldName: "beStringFactoryName",
              fixed: "left",
            },
            {
              columnName: "核算月份",
              fieldName: "accountingMonth",
              fixed: "left",
            }
          );
        }
        this.columnList = columns;
        this.handleColumns();
      });
    },
    //获取表头
    async getScenColumns() {
      const { data } = await this.$api.workbench.sceneConfigurationList("SalaryDifferenceWarningForm");
      try {
        this.columnList = [];
        const list = data.filter((item) => item.name === SCEN_NAME);

        if (!list.length || list[0].columns.length == 0) {
          await this.getColumns();
          return;
        }

        const columns = [...list[0].columns];
        const hasFactoryName = columns.some(col => col.fieldName === "beStringFactoryName");
        const hasAccountingMonth = columns.some(col => col.fieldName === "accountingMonth");
        const abcIndex = columns.findIndex(item => item.fieldName === "serialNumber");

        if (abcIndex !== -1 && !hasFactoryName && !hasAccountingMonth) {
          columns.splice(abcIndex + 1, 0,
            {
              columnName: "工厂名称",
              fieldName: "beStringFactoryName",
              fixed: "left",
            },
            {
              columnName: "核算月份",
              fieldName: "accountingMonth",
              fixed: "left",
            }
          );
        } else if (abcIndex === -1 && !hasFactoryName && !hasAccountingMonth) {
          columns.splice(0, 0,
            {
              columnName: "工厂名称",
              fieldName: "beStringFactoryName",
              fixed: "left",
            },
            {
              columnName: "核算月份",
              fieldName: "accountingMonth",
              fixed: "left",
            }
          );
        }

        this.columnList = columns;
        await this.$nextTick();
        await this.handleColumns();

      } catch (error) {
        console.error('获取列配置失败:', error);
        this.columnList = [];
      } finally {
        this.loading = false;
      }
    },
    //获取工厂
    getFactory() {
      return this.$api.systemManage.getBasicPermission
        .getBasicPermissionAll()
        .then(({ data }) => {
          this.tabList = data.map((item) => ({
            label: item.name,
            name: item.name,
            id: item.id,
          }));
        });
    },
    //导出
    handleExport() {
      let params = {
        invalidSearch: '工资差异预警',
        ...this.filterParam,
        ...this.params,
      };
      let isFilterEmpty = this.hiddenAmount ? 1 : 0;
      this.$api.information.salarySearch.exportSalaryQuery(params, isFilterEmpty).then(() => {
        this.$message.success('导出操作成功，请前往导出记录查看详情');
      });
    },
    changeHiddenAmount(val) {
      this.hiddenAmount = val;
    },
    filterDouble(value) {
      if (!value) return "-";
      return moneyFormat(value);
    },
    filterIncomingSalary(value) {
      return value && moneyDelete(value) > 3000;
    },
    filterData(value, remark) {
      if (!["其他（暂不发放）", "其他", "正常"].includes(value)) return value;
      if (remark) {
        if (value == "其他（暂不发放）") {
          return `${value}-${remark}`;
        } else {
          return remark;
        }
      } else {
        if (value == "其他（暂不发放）") {
          return `${value}`;
        } else {
          return "";
        }
      }
    },
    //获取工资差异预警列表
    getList() {
      this.loading = true;
      this.$api.information.salarySearch
        .salaryQuery({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            invalidSearch: '工资差异预警',
            ...this.filterParam,
            ...this.params,
          },
        })
        .then(({ data: { list, total } }) => {
          this.isTableShow = false;
          this.$nextTick(() => {
            this.tableData = list.map((item, index) => ({ ...item, serialNumber: index + 1 })) || [];
            this.getScenColumns();
            this.total = total;
            this.tableKey = Math.random();
          });
        }
        );
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};

      // 重置后默认设置为上个月
      this.searchForm.startYearMonth = moment().subtract(1, "months").format("YYYY-MM");
      this.searchForm.endYearMonth = moment().subtract(1, "months").format("YYYY-MM");

      // 重置后立即查询
      this.onSearch();
    },
    handleColumns() {
      const width = document.querySelector(".vxe-table").clientWidth;
      let items = {
        serialNumber: "60",
        staffName: "100",
        staffCode: "120",
        idCard: "140"
      };
      this.columnList = this.columnList
        .map((item) => {
          if (Object.keys(items).includes(item.fieldName)) {
            Object.keys(items).forEach((key) => {
              if (key == item.fieldName) {
                item.width = items[item.fieldName];
              }
            });
          } else {
            item.width = this.flexWidth(
              item.fieldName,
              this.tableData,
              item.columnName
            );
          }
          if (
            ["序号", "员工姓名", "厂牌编号"].includes(item.columnName)
          ) {
            item.fixed = "left";
          }
          if (
            ["日均计件差异率", "日均实发差异率"].includes(item.columnName)
          ) {
            item.fixed = "right";
          }
          return item;
        });
      let totalWidth = this.columnList.reduce((pre, cur) => {
        return (pre += Number(cur.width));
      }, 0);
      if (totalWidth <= width) {
        this.columnList.forEach((item) => {
          delete item.width;
          delete item.fixed;
        });
      }
      this.loading = false;
      this.isTableShow = true;
    },
    // 验证日期范围
    validateDateRange() {
      this.$refs.searchForm.validateField(['startYearMonth', 'endYearMonth'], (errorMsg) => {
        // 如果有验证错误，展开搜索框
        if (errorMsg) {
          this.expandSearchBox();
        }
      });
    },

    // 展开搜索框
    expandSearchBox() {
      // 确保搜索框已经渲染完成
      this.$nextTick(() => {
        if (this.$refs.searchBoxRef && !this.$refs.searchBoxRef.showMore) {
          // 强制设置为可展开
          this.$refs.searchBoxRef.isShowExpand = true;
          // 展开搜索框
          this.$refs.searchBoxRef.showMore = true;
        }
      });
    },
    //搜索
    onSearch(name, data) {
      // 先验证表单
      this.$refs.searchForm.validate((valid) => {
        if (valid) {
          this.filterParam = {};
          for (const [key, val] of Object.entries(this.searchForm)) {
            if (key == 'startYearMonth' && val) {
              this.filterParam.startYearMonth = moment(val).format('YYYY-MM') || '';
            } else if (key == 'endYearMonth' && val) {
              this.filterParam.endYearMonth = moment(val).format('YYYY-MM') || '';
            } else if (typeof val !== "undefined" && val !== null && val !== "") {
              this.filterParam[key] = val;
            }
          }
          if (name && data) {
            if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
          }
          this.pageNum = 1;
          this.getList();
        } else {
          // 如果验证失败，展开搜索框
          this.expandSearchBox();
        }
      });
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    //计算表格列宽度
    flexWidth(...args) {
      return calculateTableWidth(...args);
    },
    handleCancel() {
      this.visible = false;
    },
    async hanleSuccess() {
      await this.getScenColumns(); // 重新获取列配置
      this.tableKey = Math.random();// 强制刷新表格
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}
.el-form--label-left {
  .el-form-item {
    margin-right: 16px;

    &.third {
      >>>.el-form-item__content {
        margin: 0 !important;
      }
    }
  }
}
.search-box{
  padding-top: 10px;
}
.table-panel {
  >>>.table-panel-header {
    .table-panel-header-right {
      display: flex;
      justify-content: space-between;

      .pageRight {
        margin-right: 10px;
      }
    }
  }
}


.procedure {
  >>>.el-form-item__content {
    .el-select {
      width: 100%;
    }
  }
}

>>>.el-tabs__nav-wrap::after {
  display: block;
  height: 0;
}
>>>.el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;

  }
}

/* 核算月份范围样式 */
// .range {
//   >>>.el-form-item__content {
//     display: flex;
//     align-items: center;
//   }
//   .separator {
//     margin: 0 5px;
//   }
//   .nested-form-item {
//     margin-right: 0;
//     margin-bottom: 0;
//     >>>.el-form-item__content {
//       margin-left: 0 !important;
//     }
//     >>>.el-form-item__error {
//       position: absolute;
//       top: 100%;
//       left: 0;
//     }
//   }
// }
</style>
