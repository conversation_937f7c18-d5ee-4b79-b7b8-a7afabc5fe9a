import axios from "@/utils/axios";
export const SUB_APP_CODE = "was-logistics";
//工作台-工作总览
export const logisticsWorkbench = {
  //工资表打印表头
  getPrintHeader(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/getPrintHeader`, data);
  },
  //工资表打印导出
  exportSalaryPrint(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/exportSalaryPrint`, data, {
      responseType: "bolb",
    });
  },
  //获取任务总览
  getTaskOverviewList(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/list`, data);
  },
  //获取代办任务列表
  getAgencyTasks(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/detail/list`, data);
  },
  //待办任务详情
  taskDetail(data) {
    return axios({
      url: `/${SUB_APP_CODE}/quTask/detail`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //提交
  submit(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/detail/commit`, data);
  },
  //退回
  back(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/detail/sendBack`, data);
  },
  //获取个税扣款列表
  getIndividualTaxList(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/individualIncomeTax/list`, data);
  },
  //个税扣款统计
  IndividualTaxStatistics(data) {
    return axios.post(
      `/${SUB_APP_CODE}/salary/individualIncomeTax/statistic`,
      data
    );
  },
  //编辑个税扣款
  editIndividualTax(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/individualIncomeTax/edit`, data);
  },
  //获取所属工厂列表
  getBelongFactories(data) {
    return axios.post(
      `/${SUB_APP_CODE}/salary/individualIncomeTax/belongFactories`,
      data
    );
  },
  //新增个税扣款
  addIndividualTax(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/individualIncomeTax/add`, data);
  },
  //删除个税扣款
  deleteIndividualTax(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/individualIncomeTax/delete`, data);
  },
  //个税提交
  IndividualSubmit(data) {
    return axios({
      url: `/${SUB_APP_CODE}/salary/individualIncomeTax/commit`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //个税扣款详情
  viewEdit(data) {
    return axios({
      url: `/${SUB_APP_CODE}/salary/individualIncomeTax/viewEdit`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //获取工资表
  getPayroll(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/total`, data);
  },
  //新增员工工资
  addSalary(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/addSalary`, data);
  },
  //编辑工资表工序
  editSalaryProcedure(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/editProcess`, data);
  },
  //删除工资表
  deleteSalary(params) {
    return axios({
      url: `/${SUB_APP_CODE}/salary/salary/delete`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params,
    });
  },
  //工资表--查看明细
  lookSalary(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/detail`, data);
  },
  //获取分配表
  getAllocation(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/getAllotByFactoryMonth`, data);
  },
  //编辑分配表
  editAllocation(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/allot/edit`, data);
  },
  //分配表导出
  allotExport(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/allot/export`, data);
  },
  //获取上卡现金表
  getCash(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/getBankCashByFactoryMonth`, data);
  },
  //分厂调整分页获取
  getEditSalaryList(data) {
    return axios.post(
      `/${SUB_APP_CODE}/salary/factoryEditSalary/getEditList`,
      data
    );
  },
  //分厂调整统计
  getStatistic(data) {
    return axios.post(
      `/${SUB_APP_CODE}/salary/factoryEditSalary/statistics`,
      data
    );
  },
  //分厂调整提交
  factoryEditSalarySubmit(data) {
    return axios({
      url: `/${SUB_APP_CODE}/salary/factoryEditSalary/submit`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //分厂调整余留管理
  factoryEditSalaryAdjust(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/factoryEditSalary/adjust`, data);
  },
  //分厂调整明细
  factoryEditSalaryDetail(data) {
    return axios({
      url: `/${SUB_APP_CODE}/salary/factoryEditSalary/editDetail`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //分厂调整新增
  addSaveFactoryEdit(data) {
    return axios.post(
      `/${SUB_APP_CODE}/salary/factoryEditSalary/addSaveFactoryEdit`,
      data
    );
  },
  //分厂调整编辑
  updateSaveFactoryEdit(data) {
    return axios.post(
      `/${SUB_APP_CODE}/salary/factoryEditSalary/updateSaveFactoryEdit`,
      data
    );
  },
  //分厂调整删除
  deleteFactoryEdit(data) {
    return axios({
      url: `/${SUB_APP_CODE}/salary/factoryEditSalary/deleteFactoryEdit`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //分厂调整详情
  viewFactoryEdit(data) {
    return axios({
      url: `/${SUB_APP_CODE}/salary/factoryEditSalary/viewFactoryEdit`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //分厂调整记录表
  adjustmentRecord(data) {
    return axios({
      url: `/${SUB_APP_CODE}/salary/factoryEditSalary/editRecord`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //调整前工资表列表
  beforeSalary(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/before/list`, data);
  },

  //编辑上卡现金信息
  editCach(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/bankCash/edit`, data);
  },
  //结束收集生成工资表
  finishCollecting(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/finishCollecting`, data);
  },
  //强制退回
  forceBack(data) {
    return axios({
      url: `/${SUB_APP_CODE}/quTask/fallback`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //获取待办任务任务阶段
  listProcess(data) {
    return axios({
      url: `/${SUB_APP_CODE}/quTask/listProcess`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //查询工资明细的表头
  listAllColumns(params) {
    return axios({
      url: `/${SUB_APP_CODE}/system/scene/listAllColumns`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params,
    });
  },
  //自定义场景配置新增
  sceneConfigurationAdd(data) {
    return axios.post(`/${SUB_APP_CODE}/system/scene/save`, data);
  },
  //自定义场景配置编辑
  sceneConfigurationEdit(data) {
    return axios.post(`/${SUB_APP_CODE}/system/scene/update`, data);
  },
  //自定义场景列表
  sceneConfigurationList(moduleType) {
    return axios({
      url: `/${SUB_APP_CODE}/system/scene/listAllScene`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: {
        moduleType,
      },
    });
  },
  //自定义场景配置删除
  sceneConfigurationDelete(params) {
    return axios({
      url: `/${SUB_APP_CODE}/system/scene/delete`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params,
    });
  },
  //自定义场景添加配置列
  addColumns(data) {
    return axios.post(`/${SUB_APP_CODE}/system/scene/addColumns`, data);
  },
  //特殊工资单状态统计
  specialStatistics(params) {
    return axios({
      url: `/${SUB_APP_CODE}/salary/salary/specialStatistics`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params,
    });
  },
  //是否具有总览任务操作权限
  overviewPermission(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/hasSummaryTaskPermission`, data);
  },
  //是否具有待办任务操作权限
  agencyPermission(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/hasTodoTaskPermission`, data);
  },
  //工资表明细同步更新
  salaryUpdate(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/syncRefresh`, data);
  },
  //查询考勤中的班组信息
  queryGroup(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/queryGroup`, data);
  },
  //批量备注二
  batchRemark(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/batchRemark`, data);
  },
  //财务标记提交
  financialMark(data) {
    return axios({
      url: `/${SUB_APP_CODE}/quTask/financialMark`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //汇总表
  summarySheet(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/summarySheet`, data);
  },
  //汇总财务表
  financialSheet(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/financialSheet`, data);
  },
  //编辑汇总财务表
  editSummaryFinance(data) {
    return axios.post(
      `/${SUB_APP_CODE}/salary/salary/editSummaryFinanceSheetDetail`,
      data
    );
  },
  //根据工厂和核算月份查询班组列表
  availableGroups(data) {
    return axios({
      url: `/${SUB_APP_CODE}/salary/salary/availableGroups`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  // 查询核算班组
  listGroupByAttend(data) {
    return axios.post(`/${SUB_APP_CODE}/attend/listGroupByAttend`, data);
  },
  //导出
  exportComon(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/summary/export`, data);
  },
  //分厂调整导出
  factoryEditSalaryExport(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/factoryEditSalary/export`, data);
  },
  //是否展示分配表及上卡现金表标签栏
  showTabs(data) {
    return axios({
      url: `/${SUB_APP_CODE}/quTask/showTabs`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //提交一审、提交二审、结束收集校验
  checkNode(data) {
    return axios({
      url: `/${SUB_APP_CODE}/quTask/check`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //下一节点
  nextNode(data) {
    return axios({
      url: `/${SUB_APP_CODE}/quTask/goNext`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //动态列头列表
  columnHeads(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/columnHeads`, data);
  },
  //汇总表明细
  viewDetail(data) {
    return axios.post(
      `/${SUB_APP_CODE}/salary/ManualAccounting/viewDetail`,
      data
    );
  },
  //汇总表编辑
  editManualAccounting(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/ManualAccounting/edit`, data);
  },
};
//信息台账
export const logisticsInformation = {
  //员工管理
  employee: {
    //获取员工列表
    employeeList({ pageSize, pageNum, filterData }) {
      return axios.post(`/${SUB_APP_CODE}/salary/staff/list`, {
        pageSize,
        pageNum,
        filterData,
      });
    },
    //新增或修改员工
    addEmployee(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/staff/saveOrUpdate`, data);
    },
    //删除员工
    deleteEmployee(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/staff/delete`, data);
    },
    //查询历史工号详情
    employeeDetails(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/staff/detail`, data);
    },
    //员工列表统计
    employeeStatistics(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/staff/statistic`, data);
    },
    //身份证解码
    decrypt(str) {
      return axios.get("/security/private/full/decrypt", {
        params: { str },
      });
    },
    //银行卡解码
    idCardDecrypt(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/staff/decode`, data);
    },
    //试用员工身份证解码
    idCardDecodeStaff(data) {
      return axios({
        url: `/${SUB_APP_CODE}/salary/staff/idCardDecode`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //历史工号
    historyStaff(data) {
      return axios({
        url: `/${SUB_APP_CODE}/salary/staff/history`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //解冻
    unfreezeStaff(data) {
      return axios({
        url: `/${SUB_APP_CODE}/salary/staff/unfreezeStaff`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //员工详情
    staffDetail(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/staff/staffDetail`, data);
    },
  },
  //余留管理
  remaining: {
    //获取工厂余留列表
    getRemainingList(data) {
      return axios.post(`/${SUB_APP_CODE}/info/factoryRemain/getRemain`, data);
    },
    //导出工厂余留列表
    exportRemainingList(data) {
      return axios.post(`/${SUB_APP_CODE}/info/factoryRemain/export`, data);
    },
    //添加余留
    addRemaining(data) {
      return axios.post(`/${SUB_APP_CODE}/info/factoryRemain/addNewRemain`, data);
    },
    //获取工厂详细余留
    getDetailedRemaining(data) {
      return axios.post(
        `/${SUB_APP_CODE}/info/factoryRemain/getRemainDetailByFactory`,
        data
      );
    },
    //根据月份获取余留
    getRemainByMonth(data) {
      return axios.post(
        `/${SUB_APP_CODE}/info/factoryRemain/getRemainByMonth`,
        data
      );
    },
  },
  //借支台账
  debitAccount: {
    //借支台账列表
    getDebitAccountList({ pageSize, pageNum, filterData }) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/list`, {
        pageSize,
        pageNum,
        filterData,
      });
    },
    // 借支清单列表
    getDebitDetailAccountList({ pageSize, pageNum, filterData }) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/detailList`, {
        pageSize,
        pageNum,
        filterData,
      });
    },
    // 无需分期
    noAccounting(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/noAccounting`, data);
    },
    // 借支清单统计
    debitListStatistics(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/listStatistics`, data);
    },
    //确认提交
    confirmSubmission(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/confirmCommit`, data);
    },
    //借支台账取消
    confirmCancel(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/cancel`, data);
    },
    //分期还款
    amortizationLoan(data) {
      return axios.post(
        `/${SUB_APP_CODE}/salary/borrowing/generatePeriods`,
        data
      );
    },
    //借支台账详情
    debitAccountDetails(data) {
      return axios.post(
        `/${SUB_APP_CODE}/salary/borrowing/borrowingDetail`,
        data
      );
    },
    //借支台账列表统计
    debitLedgerListStatistics(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/statistic`, data);
    },
    //批量确认借支台账
    batchConfirmDebitLedger(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/batchProcess`, data);
    },
    //退回
    back(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/sendBack`, data);
    },
    //回退无需分期
    rollBackNoAccounting(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/rollBackNoAccounting`, data);
    },

    //提交
    submit(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/borrowing/commit`, data);
    },
    //获取核算月份
    getAccountingMonth(data) {
      return axios({
        url: `/${SUB_APP_CODE}/salary/borrowing/availableMonth`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //修改实付金额
    updateActualAmount(data) {
      return axios({
        url: `/${SUB_APP_CODE}/salary/borrowing/updateActualAmount`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //批量选择总数统计
    batchConfirmCount(data) {
      return axios.post(
        `/${SUB_APP_CODE}/salary/borrowing/batchConfirmCount`,
        data
      );
    },
    //变动数据
    obsDebitChangeData(data) {
      return axios.post(`/${SUB_APP_CODE}/obsDebit/changeData`, data);
    },
    //处理变动数据
    handleChangeData(data) {
      return axios({
        url: `/${SUB_APP_CODE}/obsDebit/handle`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //物流工厂
    listLogisticsFactorys(data) {
      return axios.post(`/${SUB_APP_CODE}/quFactory/listLogisticsFactory`, data);
    },
  },
  //奖惩台账
  punishment: {
    //无需核算
    noAccounting(data) {
      return axios.post(`/${SUB_APP_CODE}/RewardPunishmentLedger/noAccounting`, data);
    },
    //获取奖惩数据
    getPunishmentList(data) {
      return axios.post(`/${SUB_APP_CODE}/info/RewardPunishment/getList`, data);
    },
    //新增奖惩
    addPunishment(data) {
      return axios.post(
        `/${SUB_APP_CODE}/info/RewardPunishment/saveOrUpdate`,
        data
      );
    },
    // 更新状态
    uploadStatus(data) {
      return axios.post(
        `/${SUB_APP_CODE}/info/RewardPunishment/updateStatus`,
        data
      );
    },
    //统计
    rewardsStatistics(data) {
      return axios.post(`/${SUB_APP_CODE}/info/RewardPunishment/statistic`, data);
    },
    //删除奖惩
    deletePunishment(data) {
      return axios({
        url: `/${SUB_APP_CODE}/info/RewardPunishment/delete`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //奖惩台账明细
    viewDetail(data) {
      return axios({
        url: `/${SUB_APP_CODE}/RewardPunishmentLedger/viewDetail`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //奖惩-修改实际扣款金额
    rewardUpdateActualAmount(data) {
      return axios({
        url: `/${SUB_APP_CODE}/RewardPunishmentLedger/updateActualAmount`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //核算
    businessAccounting(data) {
      return axios.post(
        `/${SUB_APP_CODE}/RewardPunishmentLedger/businessAccounting`,
        data
      );
    },

    //奖惩批量删除
    batchDeleteReward(data) {
      return axios.post(
        `/${SUB_APP_CODE}/RewardPunishmentLedger/batchDelete`,
        data
      );
    },
    //批量处理变动数据
    multipleHandle(data) {
      return axios.post(
        `/${SUB_APP_CODE}/hr/rewardPunish/multipleHandle`,
        data
      );
    },
  },
  //特殊工资单管理
  payrollManagement: {
    //特殊工资单列表
    specialSalaryList(data) {
      return axios.post(`/${SUB_APP_CODE}/specialSalary/list`, data);
    },
    //特殊工资单统计
    statistics(data) {
      return axios.post(`/${SUB_APP_CODE}/specialSalary/statistics`, data);
    },
    //新增特殊工资单
    addSpecialSalary(data) {
      return axios.post(`/${SUB_APP_CODE}/specialSalary/add`, data);
    },
    //编辑特殊工资单
    editSpecialSalary(data) {
      return axios.post(`/${SUB_APP_CODE}/specialSalary/update`, data);
    },
    //编辑特殊工资单详情
    editSalaryDetail() {
      return axios({
        url: `/${SUB_APP_CODE}/specialSalary/editSalaryDetail`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      });
    },
    //特殊工资单详情
    salaryDetail(data) {
      return axios({
        url: `/${SUB_APP_CODE}/specialSalary/detail`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //删除特殊工资单详情
    deleteSalaryDetail(data) {
      return axios.post(
        `/${SUB_APP_CODE}/specialSalary/deleteSalaryDetail`,
        data
      );
    },
    //根据月份查询工资单详情
    monthSalary(data) {
      return axios.post(`/${SUB_APP_CODE}/specialSalary/monthSalary`, data);
    },
    //工资单详情修改记录
    salaryDetailLog() {
      return axios({
        url: `/${SUB_APP_CODE}/specialSalary/salaryDetailLog`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      });
    },
    //批量提交
    batchSubmit(data) {
      return axios.post(`/${SUB_APP_CODE}/specialSalary/batchSubmit`, data);
    },
    //提交
    submit(data) {
      return axios({
        url: `/${SUB_APP_CODE}/specialSalary/submit`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //批量审核
    batchAudit(data) {
      return axios.post(`/${SUB_APP_CODE}/specialSalary/batchAudit`, data);
    },
    //退回
    sendBack(data) {
      return axios({
        url: `/${SUB_APP_CODE}/specialSalary/sendBack`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //退回原因
    sendBackReason(data) {
      return axios({
        url: `/${SUB_APP_CODE}/specialSalary/sendBackReason`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //作废
    revoke(data) {
      return axios({
        url: `/${SUB_APP_CODE}/specialSalary/revoke`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //通过
    audit(data) {
      return axios({
        url: `/${SUB_APP_CODE}/specialSalary/audit`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //打印次数
    increment(data) {
      return axios.post(`/${SUB_APP_CODE}/specialSalary/increment`, data);
    },
    //特殊工资单明细列表
    specialSalaryDetail(data) {
      return axios.post(`/${SUB_APP_CODE}/specialSalary/detailList`, data);
    },
    //特殊工资单扣款明细
    listDeductDetail(data) {
      return axios.post(`/${SUB_APP_CODE}/specialSalary/listDeductDetail`, data);
    },
    //获取未显示在详情中的其他扣款汇总
    getDeductTotal(data) {
      return axios.post(`/${SUB_APP_CODE}/specialSalary/getDeductTotal `, data);
    },
  },
  //成本赔偿扣款台账
  costCompensation: {
    noAccounting(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/noAccounting`, data);
    },
    //成本扣款台账列表
    deductList(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/list`, data);
    },
    //统计
    deductStatistics(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/statistic`, data);
    },
    //成本扣款台账新增
    saveDeduct(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/save`, data);
    },
    //成本扣款台账编辑
    updateDeduct(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/update`, data);
    },
    //成本扣款台账核算
    businessAccounting(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/businessAccounting`, data);
    },
    //成本扣款台账回退
    backBpmDeduct(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/backBpmDeduct`, data);
    },
    //回退无需分期
    rollBackNoAccounting(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/rollBackNoAccounting`, data);
    },
    //成本扣款台账删除
    deleteBpmDeduct(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/deleteBpmDeduct`, data);
    },
    //成本扣款台账批量核算
    batchBusinessAccounting(data) {
      return axios.post(
        `/${SUB_APP_CODE}/bpmDeduct/batchBusinessAccounting`,
        data
      );
    },
    //成本扣款台账查看详情
    detailDeduct(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/viewDetail`, data);
    },
    //成本扣款-批量回退
    batchBackBpmDeduct(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/batchBackBpmDeduct`, data);
    },
    //成本扣款-批量删除
    batchDeleteBpmDeduct(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeduct/batchDeleteBpmDeduct`, data);
    },

    //成本扣款-修改实际扣款金额
    updateActualAmount(data) {
      return axios({
        url: `/${SUB_APP_CODE}/bpmDeduct/updateActualAmount`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },

    //实际扣款厂牌下拉
    getAllStaffCode(data) {
      return axios({
        url: `/${SUB_APP_CODE}/salary/staff/getAllStaffCode`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  //未付款台账
  unpaidLedger: {
    //未付款台账列表
    unPaidList(data) {
      return axios.post(`/${SUB_APP_CODE}/unPaid/list`, data);
    },
    //编辑未付款台账
    editUnPaidList(data) {
      return axios.post(`/${SUB_APP_CODE}/unPaid/edit`, data);
    },
    //删除未付款台账
    deleteUnPaid(data) {
      return axios({
        url: `/${SUB_APP_CODE}/unPaid/delete`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //申请付款
    unPaidSupplyPay(data) {
      return axios.post(`/${SUB_APP_CODE}/unPaid/supplyPay`, data);
    },
    //取消申请
    unPaidCancelSupply(data) {
      return axios.post(`/${SUB_APP_CODE}/unPaid/cancelSupply`, data);
    },
    //付款确认
    unPaidPayConfirm(data) {
      return axios.post(`/${SUB_APP_CODE}/unPaid/payConfirm`, data);
    },
    //扣款备注
    unPaidPayRemark(data) {
      return axios.post(`/${SUB_APP_CODE}/unPaid/remark`, data);
    },
    //未付款台账详情
    unPaidDetail(data) {
      return axios({
        url: `/${SUB_APP_CODE}/unPaid/detail`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  //工资查询
  salarySearch: {
    //工资查询列表
    salaryQuery(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/salary/salaryQuery`, data);
    },
    //表头
    salaryQueryColumns() {
      return axios.get(`/${SUB_APP_CODE}/salary/salary/salaryQueryColumns`);
    },
    //导出
    exportSalaryQuery(data, isFilterEmpty) {
      return axios.post(`/${SUB_APP_CODE}/salary/salary/exportSalaryQuery?isFilterEmpty=` + isFilterEmpty, data);
    },
  },
  //奖惩台账
  rewardLedger: {
    //奖惩台账列表
    rewardList(data) {
      return axios.post(`/${SUB_APP_CODE}/RewardPunishmentLedger/getList`, data);
    },
    //HR变动数据
    hrChangeData(data) {
      return axios.post(`/${SUB_APP_CODE}/hr/rewardPunish/hrChangeData`, data);
    },
    //统计
    rewardStatistic(data) {
      return axios.post(
        `/${SUB_APP_CODE}/RewardPunishmentLedger/statistic`,
        data
      );
    },
    //新增
    addReward(data) {
      return axios.post(`/${SUB_APP_CODE}/RewardPunishmentLedger/save`, data);
    },
    //奖惩台账明细
    viewDetail(data) {
      return axios({
        url: `/${SUB_APP_CODE}/RewardPunishmentLedger/viewDetail`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //删除
    deleteReward(data) {
      return axios({
        url: `/${SUB_APP_CODE}/RewardPunishmentLedger/delete`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //处理变动数据
    rewardPunish(data) {
      return axios({
        url: `/${SUB_APP_CODE}/hr/rewardPunish/handle`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //退回||批量退回
    rollback(data) {
      return axios.post(`/${SUB_APP_CODE}/RewardPunishmentLedger/rollback`, data);
    },
    //回退无需分期
    rollBackNoAccounting(data) {
      return axios.post(`/${SUB_APP_CODE}/RewardPunishmentLedger/rollBackNoAccounting`, data);
    },

    //核算
    businessAccounting(data) {
      return axios.post(
        `/${SUB_APP_CODE}/RewardPunishmentLedger/businessAccounting`,
        data
      );
    },
    //批量核算
    batchBusinessAccounting(data) {
      return axios.post(
        `/${SUB_APP_CODE}/RewardPunishmentLedger/batchBusinessAccounting`,
        data
      );
    },
    //实际扣款厂牌下拉
    getAllStaffCode(data) {
      return axios({
        url: `/${SUB_APP_CODE}/salary/staff/getAllStaffCode`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //物流工厂
    listLogisticsFactory(data) {
      return axios.post(`/${SUB_APP_CODE}/quFactory/listLogisticsFactory`, data);
    },
  },
};
//数据上传
export const logisticsDataUpload = {
  //员工考勤
  employeeAttendance: {
    //获取员工考勤列表
    getEmployeeAttendanceList(data) {
      return axios.post(`/${SUB_APP_CODE}/attend/getAttendanceList`, data);
    },
    //新增员工考勤
    addAttendance(data) {
      return axios.post(`/${SUB_APP_CODE}/attend/add`, data);
    },
    // 编辑修改员工考勤
    reviseEmployee(data) {
      return axios.post(`/${SUB_APP_CODE}/attend/update`, data);
    },
    //员工考勤列表统计
    employeeAttendanceStatistics(data) {
      return axios.post(`/${SUB_APP_CODE}/attend/statistic`, data);
    },
    //删除员工考勤
    deleteEmployee(params) {
      return axios({
        url: `/${SUB_APP_CODE}/attend/delete`,
        method: "get",
        params,
      });
    },
    //根据工厂和核算月份查询班组列表
    getGroups(data) {
      return axios({
        url: `/${SUB_APP_CODE}/attend/groups`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //查询员工关联考勤
    queryStaff(data) {
      return axios.post(`/${SUB_APP_CODE}/attend/queryStaff`, data);
    },
  },
  //计件工资
  pieceRateWage: {
    //获取班组计件工资列表
    listPieceWageGroup(data) {
      return axios.post(`/${SUB_APP_CODE}/piece/listPieceWageGroup`, data);
    },
    //获取个人计件工资列表
    listPieceWagePerson(data) {
      return axios.post(`/${SUB_APP_CODE}/piece/listPieceWagePerson`, data);
    },
    //获取班组下标总记录
    groupStatistics(data) {
      return axios.post(`/${SUB_APP_CODE}/piece/groupStatistics`, data);
    },
    //获取个人下标总记录
    personStatistics(data) {
      return axios.post(`/${SUB_APP_CODE}/piece/personStatistics`, data);
    },
    //班组删除
    deletePieceWageGroup(data) {
      return axios({
        url: `/${SUB_APP_CODE}/piece/deletePieceWageGroup`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //个人删除
    deletePieceWagePerson(data) {
      return axios({
        url: `/${SUB_APP_CODE}/piece/deletePieceWagePerson`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //根据核算班组获取出勤人员列表
    listAttndStaffByGroup(data) {
      return axios.post(
        `/${SUB_APP_CODE}/notApportion/listAttndStaffByGroup`,
        data
      );
    },
    //设置分摊
    apportion(data) {
      return axios.post(`/${SUB_APP_CODE}/piece/apportion`, data);
    },
    //批量设置分摊
    batchApportion(data) {
      return axios.post(`/${SUB_APP_CODE}/piece/batchApportion`, data);
    },
    //获取未参与分摊员工列表
    listNotShareStaff(data) {
      return axios.post(`/${SUB_APP_CODE}/notApportion/listNotShareStaff`, data);
    },
    //计件工资-班组新增
    addGroupWage(data) {
      return axios.post(`/${SUB_APP_CODE}/piece/addGroupWage`, data);
    },
    //计件工资-班组编辑
    editGroupWage(data) {
      return axios.post(`/${SUB_APP_CODE}/piece/editGroupWage`, data);
    },
    //计件工资-个人新增
    addPersonWage(data) {
      return axios.post(`/${SUB_APP_CODE}/piece/addPersonWage`, data);
    },
    //计件工资-个人编辑
    editPersonWage(data) {
      return axios.post(`/${SUB_APP_CODE}/piece/editPersonWage`, data);
    },
    //查询历史工号详情
    employeeQueryStaff(data) {
      return axios.post(`/${SUB_APP_CODE}/attend/queryStaff`, data);
    },
  },
  // 集体账户
  collectiveAccount: {
    //获取集体账户列表
    getCollectiveAccountList({ pageNum, pageSize, filterData }) {
      return axios.post(
        `/${SUB_APP_CODE}/salary/collectiveAccount/getCollective`,
        { pageNum, pageSize, filterData }
      );
    },
    //获取下标总记录
    getCollectiveAccount(data) {
      return axios.post(`/${SUB_APP_CODE}/piece/getAllDetail`, data);
    },
    //调账
    accountAdjustment(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/collectiveAccount/adjust`, data);
    },
  },
  //其他补贴
  otherSubsidies: {
    //其他补贴分页查询
    getOtherSubsidyList(data) {
      return axios.post(`/${SUB_APP_CODE}/otherSubsidy/list`, data);
    },
    //其他补贴新增
    getOtherSubsidySave(data) {
      return axios.post(`/${SUB_APP_CODE}/otherSubsidy/save`, data);
    },
    // 其他补贴编辑
    getOtherSubsidyUpdate(data) {
      return axios.post(`/${SUB_APP_CODE}/otherSubsidy/update`, data);
    },
    // 其他补贴删除
    getOtherSubsidyDelete(data) {
      return axios.post(`/${SUB_APP_CODE}/otherSubsidy/delete`, data);
    },
    // 其他补贴提交
    getOtherSubsidySubmit({ id, status }) {
      return axios.post(`/${SUB_APP_CODE}/otherSubsidy/submit`, {
        id,
        status,
      });
    },
  },
  //其他扣款
  OtherDeductions: {
    //其他扣款分页查询
    getotherDeductionList(data) {
      return axios.post(`/${SUB_APP_CODE}/otherDeduction/list`, data);
    },
    //其他扣款新增
    getOtherrDeductionSave(data) {
      return axios.post(`/${SUB_APP_CODE}/otherDeduction/save`, data);
    },
    // 其他扣款编辑
    getOtherDeductionUpdate(data) {
      return axios.post(`/${SUB_APP_CODE}/otherDeduction/update`, data);
    },
    // 其他扣款删除
    getOtherDeductionDelete(data) {
      return axios.post(`/${SUB_APP_CODE}/otherDeduction/delete`, data);
    },
    // 其他扣款提交
    getOtherDeductionSubmit(data) {
      return axios.post(`/${SUB_APP_CODE}/otherDeduction/submit`, data);
    },
    // 其他扣款动态列头
    getOtherDeductionHead({ planid }) {
      return axios({
        url: `/${SUB_APP_CODE}/otherDeduction/head`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: planid,
      });
    },
    // 其他扣款动态列数据
    getOtherDeductionHeadData(data) {
      return axios({
        url: `/${SUB_APP_CODE}/otherDeduction/data`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //其他扣款导入
    Otherimport(data) {
      return axios.post(`/${SUB_APP_CODE}/otherDeduction/import`, data);
    },
  },
  // 社保扣款
  SocialSecurity: {
    // 查询社保扣款列表
    getSocialSecurityList(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/socialSecurity/list`, data);
    },

    batchOnOrOffInsurance(data) {
      return axios.post(
        `/${SUB_APP_CODE}/salary/socialSecurity/batchOnOrOffInsurance`,
        data
      );
    },
    //批量删除
    batchDelete(data) {
      return axios.post(
        `/${SUB_APP_CODE}/salary/socialSecurity/batchDelete`,
        data
      );
    },
    // 新增-修改社保扣款
    getSocialSecuritySaveOrUpdate(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/socialSecurity/add`, data);
    },
    // 修改社保扣款
    getSocialSecuritySaveOrEdit(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/socialSecurity/edit`, data);
    },
    // 删除社保扣款
    getSocialSecurityDelete(data) {
      return axios({
        url: `/${SUB_APP_CODE}/salary/socialSecurity/delete`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    // 社保扣款列表页面统计
    getSocialSecurityStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/socialSecurity/statistic`, data);
    },
    //社保扣款查看明细
    viewEdit(data) {
      return axios({
        url: `/${SUB_APP_CODE}/salary/socialSecurity/viewEdit`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    // 20元社保扣款明细
    twenties(data) {
      return axios.post(`/${SUB_APP_CODE}/salary/socialSecurity/twenties`, data);
    },
  },
  // 未打卡扣款
  noPunchDeduction: {
    //未打卡扣款列表
    getNoPunchDeduction(data) {
      return axios.post(`/${SUB_APP_CODE}/unClock/list`, data);
    },
    //查询考勤中的班组信息
    getListGroups(data) {
      return axios.post(`/${SUB_APP_CODE}/unClock/listGroupsByCommonQuery`, data);
    },
    //新增未打卡扣款
    addUnClock(data) {
      return axios.post(`/${SUB_APP_CODE}/unClock/addUnClock`, data);
    },
    //编辑未打卡扣款
    editNoPunchDeduction(data) {
      return axios.post(`/${SUB_APP_CODE}/unClock/editUnClock`, data);
    },
    //删除未打卡扣款
    deleteUnClock(data) {
      return axios({
        url: `/${SUB_APP_CODE}/unClock/deleteUnClock`,
        method: "get",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //未打卡扣款统计
    statisticsNoPunchDeduction(data) {
      return axios.post(`/${SUB_APP_CODE}/unClock/statistics`, data);
    },
  },
  // 工会费
  unionFee: {
    //工会费列表
    getUnionFee(data) {
      return axios.post(`/${SUB_APP_CODE}/unionFees/list`, data);
    },
    //编辑工会费
    editUnionFee(data) {
      return axios.post(`/${SUB_APP_CODE}/unionFees/editUnionFees`, data);
    },
    //工会费统计
    statistictUnionFee(data) {
      return axios.post(`/${SUB_APP_CODE}/unionFees/statistics`, data);
    },
    //工会费详情
    detailUnionFee(data) {
      return axios({
        url: `/${SUB_APP_CODE}/unionFees/detail`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //批量开启或关闭工会费扣款
    batchOnOrOffUnionFees(data) {
      return axios.post(`/${SUB_APP_CODE}/unionFees/batchOnOrOffUnionFees`, data);
    },
  },
  //厂服扣款
  factoryUniform: {
    //厂服扣款列表
    getFactoryUniform(data) {
      return axios.post(`/${SUB_APP_CODE}/factoryUniform/list`, data);
    },
    //厂服扣款统计
    factoryUniformStatistics(data) {
      return axios.post(`/${SUB_APP_CODE}/factoryUniform/statistics`, data);
    },
  },
  //公司补贴
  coSubsidy: {
    //公司补贴列表
    getCoSubsidy(data) {
      return axios.post(`/${SUB_APP_CODE}/coAllowance/list`, data);
    },
    //公司补贴统计
    coSubsidyStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/coAllowance/statistics`, data);
    },
  },
  //厂牌扣款
  factoryCard: {
    //厂牌扣款列表
    getFactoryCard(data) {
      return axios.post(`/${SUB_APP_CODE}/factoryCard/list`, data);
    },
    //厂牌扣款统计
    factoryCardStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/factoryCard/statistics`, data);
    },
  },
  //住房补贴
  rentalAllowance: {
    //住房补贴列表
    getRentalAllowance(data) {
      return axios.post(`/${SUB_APP_CODE}/rentalAllowance/list`, data);
    },
    //住房补贴统计
    rentalAllowanceStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/rentalAllowance/statistics`, data);
    },
  },
  //生活费
  livingCosts: {
    //生活费列表
    getLivingCosts(data) {
      return axios.post(`/${SUB_APP_CODE}/livingCosts/list`, data);
    },
    //生活费统计
    livingCostsStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/livingCosts/statistics`, data);
    },
  },
  //体检费
  physicalExam: {
    //体检费列表
    getPhysicalExam(data) {
      return axios.post(`/${SUB_APP_CODE}/physicalExam/list`, data);
    },
    //体检费统计
    physicalExamStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/physicalExam/statistics`, data);
    },
    //体检费明细
    physicalDetail(data) {
      return axios.post(`/${SUB_APP_CODE}/physicalExam/viewDetail`, data);
    },
    //体检费编辑
    editSave(data) {
      return axios.post(`/${SUB_APP_CODE}/physicalExam/editSave`, data);
    },
  },
  //成本赔偿
  costCompensation: {
    //成本赔偿列表
    getCostCompensation(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeductList/list`, data);
    },
    //成本赔偿统计
    costCompensationStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/bpmDeductList/statistic`, data);
    },
  },
  //返工扣款
  reworkCut: {
    //返工扣款列表
    getReworkCut(data) {
      return axios.post(`/${SUB_APP_CODE}/reworkCut/list`, data);
    },
    //返工扣款统计
    reworkCutStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/reworkCut/statistics`, data);
    },
  },
  //职务补贴
  positionAllowance: {
    //列表
    getDutyList(data) {
      return axios.post(`/${SUB_APP_CODE}/duty/list`, data);
    },
    //统计
    dutyStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/duty/statistic`, data);
    },
    //新增
    addDuty(data) {
      return axios.post(`/${SUB_APP_CODE}/duty/add`, data);
    },
    //删除
    removeDuty(data) {
      return axios({
        url: `/${SUB_APP_CODE}/duty/remove`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },

    //编辑
    editDuty(data) {
      return axios({
        url: `/${SUB_APP_CODE}/duty/edit`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  //保底工资
  guaranteedSalary: {
    //保底工资列表
    staffMiniWageList(data) {
      return axios.post(`/${SUB_APP_CODE}/staffMiniWage/list`, data);
    },
    //保底工资统计
    staffMiniWageStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/staffMiniWage/statistic`, data);
    },
    //保底工资新增
    addStaffMiniWage(data) {
      return axios.post(`/${SUB_APP_CODE}/staffMiniWage/add`, data);
    },
    //保底工资编辑
    editStaffMiniWage(data) {
      return axios.post(`/${SUB_APP_CODE}/staffMiniWage/edit`, data);
    },
    //保底工资获取明细
    getDetail(data) {
      return axios.post(`/${SUB_APP_CODE}/staffMiniWage/getDetail`, data);
    },
    //保底工资同步更新
    syncUpdate(data) {
      return axios({
        url: `/${SUB_APP_CODE}/staffMiniWage/syncUpdate`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //保底工资批量删除
    batchDelete(data) {
      return axios.post(`/${SUB_APP_CODE}/staffMiniWage/remove`, data);
    },
  },
  //基本工资
  basicSalary: {
    //基本工资列表
    baseWageList(data) {
      return axios.post(`/${SUB_APP_CODE}/baseWage/list`, data);
    },
    //基本工资列表
    baseWageStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/baseWage/statistic`, data);
    },
    //编辑基本工资
    editBaseWage(data) {
      return axios({
        url: `/${SUB_APP_CODE}/baseWage/edit`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    addbaseWage(data) {
      return axios.post(`/${SUB_APP_CODE}/baseWage/add`, data);
    },
    removeWage(data) {
      return axios({
        url: `/${SUB_APP_CODE}/baseWage/remove`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },

    getOptionWage(data) {
      return axios({
        url: `/${SUB_APP_CODE}/system/option/list`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  //杂工考勤
  miscellaneous: {
    //杂工考勤列表
    getMiscellaneousList(data) {
      return axios.post(`/${SUB_APP_CODE}/backManAttendance/list`, data);
    },
    //杂工考勤统计
    getMiscellaneousStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/backManAttendance/statistic`, data);
    },
  },
  //师带徒补贴
  masterApprentice: {
    //师带徒补贴列表
    getMentorshipList(data) {
      return axios.post(`/${SUB_APP_CODE}/masterApprentice/list`, data);
    },
    //师带徒补贴统计
    getMentorshipStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/masterApprentice/statistic`, data);
    },
  },
  //技术补贴
  mentorship: {
    //技术补贴列表
    getTechList(data) {
      return axios.post(`/${SUB_APP_CODE}/tech/list`, data);
    },
    //技术补贴统计
    getTechStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/tech/statistic`, data);
    },
    //编辑技术补贴
    editTech(data) {
      return axios({
        url: `/${SUB_APP_CODE}/tech/edit`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  //用餐生活补贴
  mealExpense: {
    //用餐生活补贴列表
    getMealExpenseList(data) {
      return axios.post(`/${SUB_APP_CODE}/mealExpense/list`, data);
    },
    //用餐生活补贴统计
    getMealExpenseStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/mealExpense/statistic`, data);
    },
    //编辑用餐生活补贴
    editMealExpense(data) {
      return axios({
        url: `/${SUB_APP_CODE}/mealExpense/edit`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  //防洪补贴
  preventFlood: {
    //防洪补贴列表
    getPreventFloodList(data) {
      return axios.post(`/${SUB_APP_CODE}/preventFlood/list`, data);
    },
    //防洪补贴统计
    getPreventFloodStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/preventFlood/statistic`, data);
    },
    //编辑防洪补贴
    editPreventFlood(data) {
      return axios({
        url: `/${SUB_APP_CODE}/preventFlood/edit`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  //内购补贴
  purchase: {
    //内购补贴列表
    getPurchaseList(data) {
      return axios.post(`/${SUB_APP_CODE}/purchase/list`, data);
    },
    //内购补贴统计
    getPurchaseStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/purchase/statistic`, data);
    },
    //编辑内购补贴
    editPurchase(data) {
      return axios({
        url: `/${SUB_APP_CODE}/purchase/edit`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  //加货补贴
  addGoods: {
    //加货补贴列表
    getAddGoodsList(data) {
      return axios.post(`/${SUB_APP_CODE}/addGoods/list`, data);
    },
    //加货补贴统计
    getAddGoodsStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/addGoods/statistic`, data);
    },
    //编辑加货补贴
    editAddGoods(data) {
      return axios({
        url: `/${SUB_APP_CODE}/addGoods/edit`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  //叉车培训费
  forklift: {
    //叉车培训费列表
    getForkliftList(data) {
      return axios.post(`/${SUB_APP_CODE}/forklift/list`, data);
    },
    //叉车培训费统计
    getForkliftStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/forklift/statistic`, data);
    },
    //编辑叉车培训费
    editForklift(data) {
      return axios({
        url: `/${SUB_APP_CODE}/forklift/edit`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  //高温补贴
  highTemp: {
    //高温补贴列表
    getHighTempList(data) {
      return axios.post(`/${SUB_APP_CODE}/highTemp/list`, data);
    },
    //高温补贴统计
    getHighTempStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/highTemp/statistic`, data);
    },
    //编辑高温补贴
    editHighTemp(data) {
      return axios({
        url: `/${SUB_APP_CODE}/highTemp/edit`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  //风险工资
  riskWage: {
    //风险工资列表
    getRiskWageList(data) {
      return axios.post(`/${SUB_APP_CODE}/riskWage/list`, data);
    },
    //风险工资统计
    getRiskWageStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/riskWage/statistic`, data);
    },
    //编辑风险工资
    editRiskWage(data) {
      return axios({
        url: `/${SUB_APP_CODE}/riskWage/edit`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  //加班补贴
  overtimeWage: {
    //风险工资列表
    getOvertimeWageList(data) {
      return axios.post(`/${SUB_APP_CODE}/overtimeWage/list`, data);
    },
    //风险工资统计
    getOvertimeWageStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/overtimeWage/statistic`, data);
    },
    //编辑风险工资
    editOvertimeWage(data) {
      return axios({
        url: `/${SUB_APP_CODE}/overtimeWage/edit`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  //新员工培训补贴
  training: {
    //新员工培训补贴列表
    getTrainingList(data) {
      return axios.post(`/${SUB_APP_CODE}/training/list`, data);
    },
    //新员工培训补贴统计
    getTrainingStatistic(data) {
      return axios.post(`/${SUB_APP_CODE}/training/statistic`, data);
    },
    //编辑新员工培训补贴
    editTraining(data) {
      return axios({
        url: `/${SUB_APP_CODE}/training/edit`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  //雨布补贴
  waterproof: {
    //批量设置分摊
    batchApportion(data) {
      return axios.post(`/${SUB_APP_CODE}/waterproof/batchApportion`, data);
    },

    //获取班组雨布补贴列表
    listWithGroup(data) {
      return axios.post(`/${SUB_APP_CODE}/waterproof/listWithGroup`, data);
    },
    //获取个人雨布补贴列表
    listWithPerson(data) {
      return axios.post(`/${SUB_APP_CODE}/waterproof/listWithPerson`, data);
    },
    //获取班组下标总记录
    stsWithGroup(data) {
      return axios.post(`/${SUB_APP_CODE}/waterproof/stsWithGroup`, data);
    },
    //获取个人下标总记录
    stsWithPerson(data) {
      return axios.post(`/${SUB_APP_CODE}/waterproof/stsWithPerson`, data);
    },
    //班组删除
    deleteWithGroup(data) {
      return axios({
        url: `/${SUB_APP_CODE}/waterproof/deleteWithGroup`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //个人删除
    deleteWithPerson(data) {
      return axios({
        url: `/${SUB_APP_CODE}/waterproof/deleteWithPerson`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //设置分摊
    apportion(data) {
      return axios.post(`/${SUB_APP_CODE}/waterproof/apportion`, data);
    },
  },
  //外派补贴
  outwork: {
    //获取班组外派补贴列表
    listWithGroup(data) {
      return axios.post(`/${SUB_APP_CODE}/outwork/listWithGroup`, data);
    },
    //获取个人外派补贴列表
    listWithPerson(data) {
      return axios.post(`/${SUB_APP_CODE}/outwork/listWithPerson`, data);
    },
    //获取班组下标总记录
    stsWithGroup(data) {
      return axios.post(`/${SUB_APP_CODE}/outwork/stsWithGroup`, data);
    },
    //获取个人下标总记录
    stsWithPerson(data) {
      return axios.post(`/${SUB_APP_CODE}/outwork/stsWithPerson`, data);
    },
    //班组删除
    deleteWithGroup(data) {
      return axios({
        url: `/${SUB_APP_CODE}/outwork/deleteWithGroup`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //个人删除
    deleteWithPerson(data) {
      return axios({
        url: `/${SUB_APP_CODE}/outwork/deleteWithPerson`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //设置分摊
    apportion(data) {
      return axios.post(`/${SUB_APP_CODE}/outwork/apportion`, data);
    },

    //设置批量分摊
    batchApportion(data) {
      return axios.post(`/${SUB_APP_CODE}/outwork/batchApportion`, data);
    },
  },
  //卸草垫子补贴
  strawMattress: {
    //获取班组卸草垫子补贴列表
    listWithGroup(data) {
      return axios.post(`/${SUB_APP_CODE}/strawMattress/listWithGroup`, data);
    },
    //获取个人卸草垫子补贴列表
    listWithPerson(data) {
      return axios.post(`/${SUB_APP_CODE}/strawMattress/listWithPerson`, data);
    },
    //获取班组下标总记录
    stsWithGroup(data) {
      return axios.post(`/${SUB_APP_CODE}/strawMattress/stsWithGroup`, data);
    },
    //获取个人下标总记录
    stsWithPerson(data) {
      return axios.post(`/${SUB_APP_CODE}/strawMattress/stsWithPerson`, data);
    },
    //班组删除
    deleteWithGroup(data) {
      return axios({
        url: `/${SUB_APP_CODE}/strawMattress/deleteWithGroup`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //设置分摊
    apportion(data) {
      return axios.post(`/${SUB_APP_CODE}/strawMattress/apportion`, data);
    },
    //设置批量分摊
    batchApportion(data) {
      return axios.post(`/${SUB_APP_CODE}/strawMattress/batchApportion`, data);
    },
  },
  //杂工计件
  backMan: {
    //获取班组杂工计件列表
    listWithGroup(data) {
      return axios.post(`/${SUB_APP_CODE}/backMan/listWithGroup`, data);
    },
    //获取个人杂工计件列表
    listWithPerson(data) {
      return axios.post(`/${SUB_APP_CODE}/backMan/listWithPerson`, data);
    },
    //获取班组下标总记录
    stsWithGroup(data) {
      return axios.post(`/${SUB_APP_CODE}/backMan/stsWithGroup`, data);
    },
    //获取个人下标总记录
    stsWithPerson(data) {
      return axios.post(`/${SUB_APP_CODE}/backMan/stsWithPerson`, data);
    },
    //班组删除
    deleteWithGroup(data) {
      return axios({
        url: `/${SUB_APP_CODE}/backMan/deleteWithGroup`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //设置分摊
    apportion(data) {
      return axios.post(`/${SUB_APP_CODE}/backMan/apportion`, data);
    },
    //批量设置分摊
    batchApportion(data) {
      return axios.post(`/${SUB_APP_CODE}/backMan/batchApportion`, data);
    },

    // 杂工计件个人删除
    delete(data) {
      return axios({
        method: "post",
        url: `/${SUB_APP_CODE}/backMan/deleteWithPerson`,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  //无计件补贴
  noPiece: {
    //获取班组无计件补贴列表
    listWithGroup(data) {
      return axios.post(`/${SUB_APP_CODE}/noPiece/listWithGroup`, data);
    },
    //获取个人无计件补贴列表
    listWithPerson(data) {
      return axios.post(`/${SUB_APP_CODE}/noPiece/listWithPerson`, data);
    },
    //获取班组下标总记录
    stsWithGroup(data) {
      return axios.post(`/${SUB_APP_CODE}/noPiece/stsWithGroup`, data);
    },
    //获取个人下标总记录
    stsWithPerson(data) {
      return axios.post(`/${SUB_APP_CODE}/noPiece/stsWithPerson`, data);
    },
    //班组删除
    deleteWithGroup(data) {
      return axios({
        url: `/${SUB_APP_CODE}/noPiece/deleteWithGroup`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //个人删除
    deleteWithPerson(data) {
      return axios({
        url: `/${SUB_APP_CODE}/noPiece/deleteWithPerson`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //设置分摊
    apportion(data) {
      return axios.post(`/${SUB_APP_CODE}/noPiece/apportion`, data);
    },

    //设置批量分摊
    batchApportion(data) {
      return axios.post(`/${SUB_APP_CODE}/noPiece/batchApportion`, data);
    },
  },
};
// 数据配置
export const logisticsDataConfiguration = {
  dataConfigList(data) {
    return axios.post(`/${SUB_APP_CODE}/data/config/list`, data);
  },
  dataConfigSave(data) {
    return axios.post(`/${SUB_APP_CODE}/data/config/save`, data);
  },
  dataConfigUpdate(data) {
    return axios.post(`/${SUB_APP_CODE}/data/config/update`, data);
  },
  dataConfigToggle(data) {
    return axios.get(`/${SUB_APP_CODE}/data/config/toggle?id=${data}`);
  },
  getConfiglistAll(data) {
    return axios.post(`/${SUB_APP_CODE}/system/option/listAll`, data);
  },
  dataUpdate(data) {
    return axios.post(`/${SUB_APP_CODE}/system/option/update`, data);
  },
};
// 系统配置
export const logisticsSystemConfig = {
  // 未关联配置项查询
  listUnassignedItem(data) {
    return axios.post(`/${SUB_APP_CODE}/data/config/listUnassignedItem`, data);
  },
  //任务配置分页查询
  taskConfigPage({ pageNum, pageSize, filterData }) {
    return axios.post(`/${SUB_APP_CODE}/system/other/plan/list`, {
      pageNum,
      pageSize,
      filterData,
    });
  },
  //任务配置新增
  taskConfigAdd(data) {
    return axios.post(`/${SUB_APP_CODE}/system/other/plan/save`, data);
  },
  // 启用禁用
  taskConfigtToggle(data) {
    return axios.get(`/${SUB_APP_CODE}/system/other/plan/toggle?id=${data}`);
  },
  //任务配置编辑
  taskConfigEdit(data) {
    return axios.post(`/${SUB_APP_CODE}/system/other/plan/update`, data);
  },
  //任务配置删除
  taskConfigDel(data) {
    return axios({
      url: `/${SUB_APP_CODE}/system/other/plan/delete`,
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //任务配置模板下载
  taskConfigDownload(data) {
    return axios({
      url: `/${SUB_APP_CODE}/system/other/plan/template/download`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      responseType: "blob",
      params: data,
    });
  },
  //页面配置查询
  pageConfig() {
    return axios.post(`/${SUB_APP_CODE}/system/other/page/list`);
  },
  //页面配置空数据隐藏或显示
  pageConfigEmptyData(data) {
    return axios.post(`/${SUB_APP_CODE}/system/other/page/showOrHide`, data);
  },
  //页面配置拖拽排序
  pageConfigdDrag(data) {
    return axios.post(`/${SUB_APP_CODE}/system/other/page/drag`, data);
  },
  //动态列头
  dynamicheader(params) {
    return axios.get(`/${SUB_APP_CODE}/system/other/head`, {
      params,
    });
  },
};
//系统管理
export const logisticsSystemManage = {
  //基础信息
  getBasicPermission: {
    //获取所有带有数据权限的工厂
    getBasicPermissionAll(data) {
      return axios.post(`/${SUB_APP_CODE}/quFactory/getDetail`, data);
    },
    //获取所有工厂
    getQuFactory(data) {
      return axios({
        url: `/${SUB_APP_CODE}/quFactory/listByAll`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //可用的工厂列表
    availableFactories() {
      return axios.post(`/${SUB_APP_CODE}/system/basicConfig/availableFactories`);
    },
    //获取所有基础信息
    getAllFactory(data) {
      return axios.post(`/${SUB_APP_CODE}/quFactory/list`, data);
    },
    belongFactories(data) {
      return axios.post(
        `/${SUB_APP_CODE}/salary/socialSecurity/belongFactories`,
        data
      );
    },
    // 编辑班组信息
    editGroup(data) {
      return axios.post(`/${SUB_APP_CODE}/group/edit`, data);
    },
    // 新增班组信息
    addGroup(data) {
      return axios.post(`/${SUB_APP_CODE}/group/add`, data);
    },
    // 通过ID删除数据
    getBasicPermissionDeleteById(id) {
      return axios({
        url: `/${SUB_APP_CODE}/system/basicConfig/deleteById`,
        id,
      });
    },
    //工厂启用或禁用
    enable(data) {
      return axios({
        url: `/${SUB_APP_CODE}/quFactory/enable`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //工序启用或禁用
    groupEnable(data) {
      return axios({
        url: `/${SUB_APP_CODE}/group/enable`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //根据工厂下的班组
    availableGroup(data) {
      return axios({
        url: `/${SUB_APP_CODE}/group/list`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //根据工厂和核算月份查询班组列表
    listByFactoryId(data) {
      return axios({
        url: `/${SUB_APP_CODE}/system/basicConfig/availableGroups`,
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  // 数据权限
  dataPermission: {
    // 获取用户数据权限
    getUserPermission(data) {
      return axios.post(
        `/${SUB_APP_CODE}/salary/userPermission/getUserList`,
        data
      );
    },
    editPermission(data) {
      return axios.post(
        `/${SUB_APP_CODE}/salary/userPermission/editPermission`,
        data
      );
    },
  },
  // 系统日志
  systemLog: {
    //分页获取日志列表
    logList(data) {
      return axios.post(`/${SUB_APP_CODE}/log/list`, data);
    },
  },
};

//导出
export const logisticsExportApI = {
  //获取导入模块列表
  getImportModules() {
    return axios({
      url: `/${SUB_APP_CODE}/quTask/listImportModules`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    });
  },
  //获取导入任务
  getImportTask(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/listImportTask`, data);
  },
  otherHead(data) {
    return axios({
      url: `/${SUB_APP_CODE}/system/other/head`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
};
// 统计
export const logisticsStatiStics = {
  statisticsSalary(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/salary/statistics`, data);
  },
  statisticsBankCash(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/bankCash/statistics`, data);
  },
  statisticsAllot(data) {
    return axios.post(`/${SUB_APP_CODE}/salary/allot/statistics`, data);
  },
  // 计件工资操作权限
  getPieceWagePermission(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/getPieceWagePermission`, data);
  },
  // 其他扣款统计
  statisticsOtherDeduction(data) {
    return axios.post(`/${SUB_APP_CODE}/otherDeduction/statistics`, data);
  },
  // 其他补贴统计
  statisticsOtherSubsidy(data) {
    return axios.post(`/${SUB_APP_CODE}/otherSubsidy/statistics`, data);
  },
  statisticsOthegetCompleteTaskPermissionrSubsidy(data) {
    return axios.post(`/${SUB_APP_CODE}/quTask/getCompleteTaskPermission`, data);
  },
};

// 意外险
export const accidentRisk = {
  // 意外险列表
  getPageList(data) {
    return axios.post(`/${SUB_APP_CODE}/accidentInsurance/list`, data);
  },
  // 意外险列表统计
  statistics(data) {
    return axios.post(`/${SUB_APP_CODE}/accidentInsurance/statistics`, data);
  },
  // 新增编辑
  addOrEdit(data) {
    return axios.post(`/${SUB_APP_CODE}/accidentInsurance/addOrEdit`, data);
  },
  // 详情
  viewDetail(id) {
    return axios({
      method: "post",
      url: `/${SUB_APP_CODE}/accidentInsurance/viewDetail`,
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: { id },
    });
  },
  // 单个删除
  delete(id) {
    return axios({
      method: "post",
      url: `/${SUB_APP_CODE}/accidentInsurance/delete`,
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: id,
    });
  },
  // 批量删除
  batchDelete(data) {
    return axios.post(`/${SUB_APP_CODE}/accidentInsurance/batchDelete`, data);
  },
};

// 门槛费
export const thresholdFee = {
  // 获取列表
  getPageList(data) {
    return axios.post(`/${SUB_APP_CODE}/thresholdFee/list`, data);
  },
  // 意外险列表统计
  statistics(data) {
    return axios.post(`/${SUB_APP_CODE}/thresholdFee/statistics`, data);
  },
  // 新增编辑
  addOrEdit(data) {
    return axios.post(`/${SUB_APP_CODE}/thresholdFee/addOrEdit`, data);
  },
  // 详情
  viewDetail(id) {
    return axios({
      method: "post",
      url: `/${SUB_APP_CODE}/thresholdFee/viewDetail`,
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: { id },
    });
  },
  // 单个删除
  delete(id) {
    return axios({
      method: "post",
      url: `/${SUB_APP_CODE}/thresholdFee/delete`,
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: id,
    });
  },
  // 批量删除
  batchDelete(data) {
    return axios.post(`/${SUB_APP_CODE}/thresholdFee/batchDelete`, data);
  },
};
