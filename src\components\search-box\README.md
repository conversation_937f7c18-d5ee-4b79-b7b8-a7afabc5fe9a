# SearchBox 组件使用说明

## 优化内容

### 主要改进
1. **移除固定高度限制**：不再使用硬编码的 34px 高度
2. **灵活的高度计算**：支持自定义单行高度和最小高度
3. **事件驱动**：通过 `height-change` 事件通知父组件高度变化
4. **更好的响应式支持**：自动适应窗口大小变化
5. **改进的样式**：使用 flexbox 布局，支持内容自适应

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| defaultExpand | Boolean | false | 是否默认展开 |
| isFloating | Boolean | false | 是否浮动模式 |
| singleHeight | Number | 40 | 单行高度（px） |
| minHeight | Number | 40 | 最小高度（px） |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| height-change | { isExpanded, height, heightDiff } | 高度变化时触发 |

## 使用示例

### 基础用法
```vue
<template>
  <div class="page-container">
    <search-box @height-change="onSearchHeightChange">
      <el-form :inline="true">
        <el-form-item label="姓名">
          <el-input v-model="searchForm.name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="部门">
          <el-select v-model="searchForm.dept" placeholder="请选择部门">
            <el-option label="技术部" value="tech"></el-option>
            <el-option label="销售部" value="sales"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </search-box>
    
    <div :style="tableContainerStyle" class="table-container">
      <el-table :data="tableData" height="100%">
        <!-- 表格列定义 -->
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchForm: {
        name: '',
        dept: ''
      },
      tableData: [],
      searchHeight: 40, // 搜索框当前高度
      baseTableHeight: 'calc(100vh - 200px)' // 基础表格高度
    }
  },
  computed: {
    tableContainerStyle() {
      return {
        height: `calc(${this.baseTableHeight} - ${this.searchHeight}px)`
      }
    }
  },
  methods: {
    onSearchHeightChange({ height }) {
      this.searchHeight = height;
    },
    handleSearch() {
      // 搜索逻辑
    },
    handleReset() {
      this.searchForm = {
        name: '',
        dept: ''
      };
    }
  }
}
</script>
```

### 浮动模式
```vue
<template>
  <div class="page-container">
    <search-box :is-floating="true" :default-expand="true">
      <!-- 搜索表单内容 -->
    </search-box>
    
    <!-- 浮动模式下表格高度不受影响 -->
    <div class="table-container" style="height: calc(100vh - 150px)">
      <el-table :data="tableData" height="100%">
        <!-- 表格列定义 -->
      </el-table>
    </div>
  </div>
</template>
```

### 自定义高度
```vue
<template>
  <search-box 
    :single-height="50" 
    :min-height="45"
    @height-change="onSearchHeightChange"
  >
    <!-- 搜索表单内容 -->
  </search-box>
</template>
```

## 方法

| 方法名 | 说明 | 参数 |
|--------|------|------|
| recheckHeight | 手动触发高度检查 | - |

### 手动触发高度检查
```javascript
// 当搜索框内容动态变化时，可以手动触发高度检查
this.$refs.searchBox.recheckHeight();
```
