<template>
  <q-dialog :visible="isVisible" :isLoading="isLoading" custom-class="dialog-custom-config" title="自定义列" width="800px"
    :innerScroll="false" @close="onCancel" @cancel="onCancel" @confirm="onConfirm">
    <el-row v-loading="loading">
      <!-- 左侧表格 -->
      <el-col :span="11">
        <div class="table-title">所有数据列</div>
        <div class="table-filter-box">
          <el-input v-model.trim="searchKey1" class="form-input" size="small" clearable placeholder="输入字段名称">
          </el-input>
        </div>
        <el-table ref="mainTable" class="main-table" :data="leftTableData" height="358" border
          :header-cell-style="{ backgroundColor: '#F4F8FB' }" @select="onSelect" @select-all="onSelectAll"
          @row-click="onRowClick">
          <el-table-column width="40" type="selection" align="center">
          </el-table-column>
          <el-table-column prop="columnName" label="字段名称" align="center" show-overflow-tooltip>
          </el-table-column>
        </el-table>
      </el-col>

      <!-- 中间间隔 -->
      <el-col :span="2">
        <div class="empty-block">&nbsp;</div>
      </el-col>

      <!-- 右侧表格 -->
      <el-col :span="11">
        <div class="right-table-title">
          <div class="title">自定义展示数据列</div>
          <!-- <div>
            首部冻结
            <el-input-number v-model="frozenColumnNum" class="number-input" step-strictly :controls="false" :min="0"
              size="mini">
            </el-input-number>
            列
          </div> -->
        </div>
        <div class="table-filter-box">
          <el-input v-model.trim="searchKey2" class="form-input" size="small" clearable placeholder="输入字段名称">
          </el-input>
        </div>
        <div class="sortable-table">
          <div class="sortable-table-header">
            <div class="sortable-table-row">
              <div class="sortable-table-cell name-cell">字段名称</div>
              <div class="sortable-table-cell action-cell">操作</div>
            </div>
          </div>
          <div class="sortable-table-body" ref="tableBody">
            <draggable v-model="selectedList" draggable=".sortable-table-row">
              <div v-for="(item, index) in rightTableData" :key="item.fieldName" class="sortable-table-row">
                <div class="sortable-table-cell name-cell">
                  {{ item.columnName }}
                </div>
                <div class="sortable-table-cell action-cell">
                  <i class="icon-btn del-btn el-icon-close" @click="onRemove(item)">
                  </i>
                </div>
              </div>
            </draggable>
          </div>
        </div>
      </el-col>
    </el-row>
  </q-dialog>
</template>

<script>
import draggable from 'vuedraggable';
// 数据的唯一键
const UNIQUE_KEY = 'fieldName';
//当前场景唯一键名
const SCEN_NAME = '物流工资查询';
export default {
  name: 'logisticsSalaryCustomConfig',
  components: {
    draggable,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    isSingle: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      searchKey1: '',
      searchKey2: '',
      loading: false,
      isLoading: false,
      tableData: [],
      selectedList: [],
      frozenColumnNum: 0,
      scenInfo: {},
      // 使用 Set 结构存储选中项，提高查找效率
      selectedSet: new Set(),
      // 缓存防抖的搜索方法
      debouncedSearch: null,
    };
  },
  computed: {
    isVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      },
    },
    // 优化过滤逻辑，使用 computed 缓存结果
    leftTableData() {
      const searchKey = this.searchKey1.toLowerCase();
      return searchKey
        ? this.tableData.filter(item =>
          item.columnName.toLowerCase().includes(searchKey))
        : this.tableData;
    },
    rightTableData() {
      const searchKey = this.searchKey2.toLowerCase();
      return searchKey
        ? this.selectedList.filter(item =>
          item.columnName.toLowerCase().includes(searchKey))
        : this.selectedList;
    },
    // 添加已选择项的计算属性
    hasSelected() {
      return this.selectedList.length > 0;
    }
  },
  watch: {
    // 监听搜索关键字变化，使用防抖优化
    searchKey1: {
      handler(val) {
        if (this.debouncedSearch) {
          this.debouncedSearch(val);
        }
      }
    },
    selectedList: {
      handler(newVal) {
        // 更新 Set 结构
        this.selectedSet = new Set(newVal.map(item => item.fieldName));
      },
      deep: true
    }
  },
  created() {
    // 创建防抖搜索方法
    this.debouncedSearch = this.debounce(this.handleSearch, 300);
    this.init();
  },
  beforeDestroy() {
    // 清理工作
    this.debouncedSearch = null;
    this.selectedSet.clear();
  },
  methods: {
    // 防抖函数
    debounce(fn, delay) {
      let timer = null;
      return (...args) => {
        if (timer) clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(this, args);
        }, delay);
      };
    },

    async init() {
      try {
        this.loading = true;
        await Promise.all([
          this.getAllList(),
          this.getSceneList()
        ]);
        await this.initConfigScene();
        await this.$nextTick();
        this.checkRowChecked();
      } catch (error) {
        console.error('初始化失败:', error);
        this.$message.error('初始化失败，请刷新重试');
      } finally {
        this.loading = false;
      }
    },
    onCancel() {
      this.reset();
      this.isVisible = false;
    },
    async onConfirm() {
      if (!this.hasSelected) {
        this.$message.error('请先选择字段');
        return;
      }

      if (this.frozenColumnNum > this.selectedList.length) {
        this.$message.error('固定列数不能大于自定义字段数');
        return;
      }

      if (this.isLoading) return;

      try {
        this.isLoading = true;
        await this.$api.logisticsWorkbench.addColumns({
          sceneId: this.scenInfo.id || "",
          columns: this.selectedList.map(item => ({
            columnName: item.columnName,
            fieldName: item.fieldName,
          }))
        });

        this.$notify.success({
          title: "成功",
          message: "自定义列修改成功",
        });

        this.onCancel();
        this.$emit('success');
      } catch (error) {
        console.error('保存失败:', error);
        this.$message.error('保存失败，请重试');
      } finally {
        this.isLoading = false;
      }
    },
    reset() {
      this.selectedList = [];
      this.selectedSet.clear();
      this.searchKey1 = '';
      this.searchKey2 = '';
    },
    //初始化场景处理
    async initConfigScene() {
      const configureList = this.sceneList.find(item => item.name === SCEN_NAME);

      if (configureList) {
        this.scenInfo = configureList;
        this.selectedList = configureList.columns || [];
        return;
      }

      // 场景不存在时创建新场景
      await this.$api.logisticsWorkbench.sceneConfigurationAdd({
        moduleType: "SalaryQuery",
        name: SCEN_NAME,
      });
      await this.init();
    },
    //获取当前所有列
    async getAllList() {
      const { data } = await this.$api.logisticsInformation.salarySearch.salaryQueryColumns();
      this.tableData = data || [];
    },
    //查询所有自定义场景
    async getSceneList() {
      const { data } = await this.$api.logisticsWorkbench.sceneConfigurationList("SalaryQuery");
      this.sceneList = data || [];
    },
    onSelect(selection, row) {
      const fieldName = row.fieldName;
      if (selection.includes(row)) {
        if (!this.selectedSet.has(fieldName)) {
          this.selectedList.push(row);
        }
      } else {
        this.removeSelected(fieldName);
      }
    },
    onSelectAll(selection) {
      if (selection.length) {
        const newItems = selection.filter(row =>
          !this.selectedSet.has(row.fieldName)
        );
        this.selectedList.push(...newItems);
      } else {
        this.selectedList = [];
      }
    },
    onRowClick(row) {
      if (!this.isSingle) {
        return;
      }
      this.selectedValue = row[UNIQUE_KEY];
      this.selectedList = [row];
    },
    removeSelected(fieldName) {
      const index = this.selectedList.findIndex(item => item.fieldName === fieldName);
      if (index > -1) {
        this.selectedList.splice(index, 1);
      }
    },
    checkRowChecked() {
      const checkingRows = this.tableData.filter(row =>
        this.selectedList.some(item => item[UNIQUE_KEY] === row[UNIQUE_KEY])
      );
      checkingRows.forEach(row => {
        this.$refs.mainTable.toggleRowSelection(row, true);
      });
    },
    onRemove(row) {
      this.removeSelected(row[UNIQUE_KEY]);
      const targetRow = this.tableData.find(item => item[UNIQUE_KEY] === row[UNIQUE_KEY]);
      if (targetRow) {
        this.$refs.mainTable.toggleRowSelection(targetRow, false);
      }
      if (this.isSingle) {
        this.selectedValue = '';
      }
    },
    onRightSearch() {
      this.rightTableData = this.selectedList.filter(item => item.columnName.includes(this.searchKey2));
    },
    // 检测右表格高度，是否显示滚动条，更改表头最后一列的宽度
    observeRightTableHeight() {
      const bodyRef = this.$refs.tableBody;
      const bodyNode = document.querySelector('.sortable-table-body>div');
      const callback = (mutationsList) => {
        for (let mutation of mutationsList) {
          if (mutation.type === 'childList') {
            this.isShowEmptyCell = bodyRef.scrollHeight > bodyRef.clientHeight;
          }
        }
      };
      this.observer = new MutationObserver(callback);
      this.observer.observe(bodyNode, {
        attributes: false,
        childList: true
      });
    }
  }
};
</script>

<style lang="stylus" scoped>
.table-title
  margin-bottom 10px
  font-size 16px
  height 24px
.right-table-title
  margin-bottom 10px
  height: 24px
  display: flex
  justify-content: space-between
  align-items: center
  .title
    font-size 16px

.table-filter-box
  margin-bottom 10px
.icon-btn
  padding 2px 5px
  cursor pointer
  color #24c69a
.del-btn
  color red

.sortable-table
  overflow hidden
  padding-bottom 8px
  height 350px
  width 100%
  border 1px solid #E0E6ED
  &-header
    background-color #f4f8fb
    font-weight bold
    color #666
    .header-empty-cell
      width 16px
      height 32px
      border-left 1px solid #E0E6ED
  &-body
    height calc(100% - 26px)
    overflow-y: auto

  &-row
    line-height: 1.2
    display: flex
    align-items: center
    border-bottom 1px solid #E0E6ED
    .name-cell
      flex 1
    .action-cell
      flex none
      width 40px

  &-cell
    padding 8px 2px
    text-align center
    color #333
    &:first-of-type
      border-right 1px solid #E0E6ED

.number-input
  width 50px
  line-height: 1
  >>> .el-input__inner
    padding 0 5px

.empty-block
  min-width 5px
  height 1px

</style>
