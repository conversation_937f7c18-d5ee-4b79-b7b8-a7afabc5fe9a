export default {
  data() {
    return {
      observeElement: null,
      maxTableHeight: 400,
      searchBoxHeight: 0, // 搜索框实际高度
      lastSearchBoxHeight: 0 // 上次搜索框高度
    };
  },
  watch: {
    $route(to, from) {
      if (this.visitedRoutes.includes(to.name)) {
        // 已经访问过
        setTimeout(() => {
          this.setMaxHeight();
          console.log('tableMixin watch route 1500ms later');
        }, 1500);
      } else {
        this.$store.dispatch('tagsView/addWatchedView', to);
      }
    }
  },
  computed: {
    visitedRoutes() {
      return this.$store.state.tagsView.watchedVisitedRoutes;
    }
  },
  methods: {
    // 获取搜索框的实际高度
    getSearchBoxHeight() {
      let searchHeight = 0;
      try {
        // 查找搜索框组件
        const searchBox = this.$el.querySelector('.search-outer');
        if (searchBox) {
          searchHeight = searchBox.offsetHeight;
        }
      } catch (error) {
        console.log('获取搜索框高度失败:', error);
      }
      return searchHeight;
    },

    setMaxHeight(dynamicHeight) {
      if (!this.$refs.tablePanel || !this.$refs.tablePanel.$refs || !this.$refs.tablePanel.$refs.tableWrap) {
        return;
      }

      this.$nextTick(() => {
        try {
          // 获取搜索框实际高度
          this.searchBoxHeight = this.getSearchBoxHeight();

          // 计算基础偏移量：表格header + 分页组件高度 + 间距
          const baseOffset = this.resizeOffset || 52;

          // 动态高度调整（来自搜索框展开/收起）
          const dynamicOffset = typeof dynamicHeight === 'number' ? dynamicHeight : 0;

          // 总偏移量
          const OFFSET = baseOffset + dynamicOffset;

          // 计算表格最大高度
          const tableWrapTop = this.$refs.tablePanel.$refs.tableWrap.getBoundingClientRect().top;
          this.maxTableHeight = window.innerHeight - tableWrapTop - OFFSET;

          // 确保最小高度
          if (this.maxTableHeight < 200) {
            this.maxTableHeight = 200;
          }

          // 记录当前搜索框高度
          this.lastSearchBoxHeight = this.searchBoxHeight;

        } catch (error) {
          console.log('tableMixin setMaxHeight error:', error);
        }
      });
    },

    // 强制重新计算高度（考虑搜索框高度变化）
    recalculateHeight() {
      this.$nextTick(() => {
        const currentSearchHeight = this.getSearchBoxHeight();
        const heightDiff = currentSearchHeight - this.lastSearchBoxHeight;

        if (Math.abs(heightDiff) > 5) { // 5px 容差
          this.setMaxHeight(-heightDiff); // 搜索框变高时，表格要变矮
        }
      });
    },

    addListener() {
      window.addEventListener('resize', this.setMaxHeight);
    },

    removeListener() {
      window.removeEventListener('resize', this.setMaxHeight);
    }
  },
  mounted() {
    this.addListener();

    // 延迟计算，确保所有组件都已渲染
    this.$nextTick(() => {
      setTimeout(() => {
        this.setMaxHeight();
      }, 100);
    });

    // 监听搜索框高度变化
    this.$bus.$on('table.updateHeight', (offset) => {
      this.setMaxHeight(offset || 0);
    });

    // 监听DOM变化，自动重新计算高度
    if (typeof MutationObserver !== 'undefined') {
      this.$nextTick(() => {
        const observer = new MutationObserver(() => {
          this.recalculateHeight();
        });

        if (this.$el) {
          observer.observe(this.$el, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'class']
          });

          this.mutationObserver = observer;
        }
      });
    }
  },

  beforeDestroy() {
    this.removeListener();

    // 清理 MutationObserver
    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
    }
  }
};